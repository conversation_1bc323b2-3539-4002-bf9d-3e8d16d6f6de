import { useSidebarPermissions, LOGISTICS_PERMISSIONS, LogisticsPermissionKey } from './useSidebarPermissions';

/**
 * Custom hook for logistics-specific permission checks
 * This hook provides easy access to logistics permissions and utility functions
 */
export const useLogisticsPermissions = () => {
  const { hasPermission, hasLogisticsPermission, userPermissionCodes, isLoading } = useSidebarPermissions();

  // Individual permission checks
  const canBookVisit = hasLogisticsPermission('BOOK_VISIT');
  const canCompleteTrips = hasLogisticsPermission('COMPLETE_TRIPS');
  const canCreateVehicleRequest = hasLogisticsPermission('CREATE_VEHICLE_REQUEST');
  const canCreateSpecialAssignment = hasLogisticsPermission('CREATE_SPECIAL_ASSIGNMENT');
  const canAccessLogisticsDashboard = hasLogisticsPermission('ACCESS_LOGISTICS_DASHBOARD');
  const canAccessLogisticsStatistics = hasLogisticsPermission('ACCESS_LOGISTICS_STATISTICS');
  const canAccessClients = hasLogisticsPermission('ACCESS_CLIENTS');
  const canAccessDrivers = hasLogisticsPermission('ACCESS_DRIVERS');
  const canAccessVehicles = hasLogisticsPermission('ACCESS_VEHICLES');
  const canAccessLogisticsReports = hasLogisticsPermission('ACCESS_LOGISTICS_REPORTS');
  const canAccessSiteVisitReports = hasLogisticsPermission('ACCESS_SITEVISIT_REPORTS');

  // Utility function to check multiple permissions
  const hasAnyLogisticsPermission = (permissions: LogisticsPermissionKey[]): boolean => {
    return permissions.some(permission => hasLogisticsPermission(permission));
  };

  // Utility function to check all permissions
  const hasAllLogisticsPermissions = (permissions: LogisticsPermissionKey[]): boolean => {
    return permissions.every(permission => hasLogisticsPermission(permission));
  };

  // Check if user has any logistics access at all
  const hasAnyLogisticsAccess = hasAnyLogisticsPermission([
    'BOOK_VISIT',
    'COMPLETE_TRIPS',
    'CREATE_VEHICLE_REQUEST',
    'CREATE_SPECIAL_ASSIGNMENT',
    'ACCESS_LOGISTICS_DASHBOARD',
    'ACCESS_LOGISTICS_STATISTICS',
    'ACCESS_CLIENTS',
    'ACCESS_DRIVERS',
    'ACCESS_VEHICLES',
    'ACCESS_LOGISTICS_REPORTS',
    'ACCESS_SITEVISIT_REPORTS'
  ]);

  // Get all logistics permissions the user has
  const userLogisticsPermissions = Object.entries(LOGISTICS_PERMISSIONS)
    .filter(([_, code]) => hasPermission(code))
    .map(([key, _]) => key as LogisticsPermissionKey);

  return {
    // Individual permission checks
    canBookVisit,
    canCompleteTrips,
    canCreateVehicleRequest,
    canCreateSpecialAssignment,
    canAccessLogisticsDashboard,
    canAccessLogisticsStatistics,
    canAccessClients,
    canAccessDrivers,
    canAccessVehicles,
    canAccessLogisticsReports,
    canAccessSiteVisitReports,

    // Utility functions
    hasLogisticsPermission,
    hasAnyLogisticsPermission,
    hasAllLogisticsPermissions,
    hasAnyLogisticsAccess,

    // Data
    userLogisticsPermissions,
    userPermissionCodes,
    isLoading,

    // Permission codes for reference
    LOGISTICS_PERMISSIONS,
  };
};

export default useLogisticsPermissions;
