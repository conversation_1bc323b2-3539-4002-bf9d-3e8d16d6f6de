// src/pages/LogisticsReports.tsx

import { useState, useEffect } from "react";
import { Screen } from "@/app-components/layout/screen";
import { DotBadge } from "@/components/custom/badges/badges";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import { DateRangePicker } from "@/components/custom/datepicker/DateRangePicker";
import { ReportsModal, ReportConfig, registerReportComponent } from "@/components/reports/ReportsModal";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, BarChart3, MapPin, Route, MessageSquare } from "lucide-react";
import { BASE_URL } from "@/config";

// Import your custom report components (see below for examples)
import ApprovedSiteVisitsReport from "./Reports/ApprovedSiteVisitsReports";
import SiteVisitsSummaryReport from "./Reports/SiteVisitsSummaryReport";
import MostBookedSitesReport from "./Reports/MostBookedSitesReport";
import ChauffeurItineraryReport from "./Reports/ChauffeurItineraryReport";
import MarketersFeedbackReport from "./Reports/MarketersFeedbackReport";

// Register all reports to the registry (only once, e.g. in index or this file)
registerReportComponent("approved-site-visits", ApprovedSiteVisitsReport);
registerReportComponent("site-visits-summary", SiteVisitsSummaryReport);
registerReportComponent("most-booked-sites", MostBookedSitesReport);
registerReportComponent("chauffeur-itinerary", ChauffeurItineraryReport);
registerReportComponent("marketers-feedback", MarketersFeedbackReport);

const TABS = [
  {
    key: "approved-site-visits",
    title: "Approved Visits",
    shortTitle: "Approved",
    icon: CheckCircle2,
    description: "View all approved site visits and special assignments"
  },
  { 
    key: "site-visits-summary", 
    title: "Site Visits Summary",
    shortTitle: "Summary", 
    icon: BarChart3,
    description: "Performance overview and statistics"
  },
  { 
    key: "most-booked-sites", 
    title: "Most Booked Sites",
    shortTitle: "Popular Sites",
    icon: MapPin,
    description: "Top performing and popular locations"
  },
  { 
    key: "chauffeur-itinerary", 
    title: "Chauffeur Itinerary",
    shortTitle: "Itinerary",
    icon: Route,
    description: "Driver schedules and trip details"
  },
  { 
    key: "marketers-feedback", 
    title: "Marketers' Feedback",
    shortTitle: "Feedback",
    icon: MessageSquare,
    description: "Client feedback and satisfaction ratings"
  },
];

const ENDPOINTS = {
  "approved-site-visits": `${BASE_URL}/logistics/site-visits`,
  "site-visits-summary": `${BASE_URL}/logistics/site-visits`,
  "most-booked-sites": `${BASE_URL}/logistics/site-visits`,
  "chauffeur-itinerary": `${BASE_URL}/logistics/site-visits`,
  "marketers-feedback": `${BASE_URL}/logistics/site-visits`,
};

const TITLES = {
  "approved-site-visits": "Approved Visits Report (Site Visits & Special Assignments)",
  "site-visits-summary": "Site Visits Summary",
  "most-booked-sites": "Most Booked Sites",
  "chauffeur-itinerary": "Chauffeur Itinerary",
  "marketers-feedback": "Marketers' Feedback",
};

export default function LogisticReports() {
  const [activeTab, setActiveTab] = useState<keyof typeof ENDPOINTS>("approved-site-visits");
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({ from: undefined, to: undefined });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalConfig, setModalConfig] = useState<ReportConfig | null>(null);

  // Clear form when tab changes
  useEffect(() => {
    setDateRange({ from: undefined, to: undefined });
  }, [activeTab]);

  // Build params for each report (customize as needed per endpoint)
  const getParams = () => {
    const params: Record<string, any> = {};

    // Add date range parameters with strict filtering
    if (dateRange.from) {
      // Set start of day for 'from' date to ensure we include the full day
      const fromDate = new Date(dateRange.from);
      fromDate.setHours(0, 0, 0, 0);
      params.pickup_date__gte = fromDate.toISOString().slice(0, 10);
    }
    if (dateRange.to) {
      // Set end of day for 'to' date to ensure we include the full day
      const toDate = new Date(dateRange.to);
      toDate.setHours(23, 59, 59, 999);
      params.pickup_date__lte = toDate.toISOString().slice(0, 10);
    }

    // Add pagination - increase page size to ensure we get all records in date range
    params.page = 1;
    params.page_size = 1000;

    // Filter based on report type
    switch (activeTab) {
      case 'approved-site-visits':
        params.status = 'Approved';
        break;
      case 'site-visits-summary':
        // Get all site visits for summary
        break;
      case 'most-booked-sites':
        // Get completed visits for booking analysis
        params.status = 'Completed';
        break;
      case 'chauffeur-itinerary':
        // Get approved and in-progress visits for driver schedules
        params.status__in = 'Approved,In Progress';
        break;
      case 'marketers-feedback':
        // Get completed visits for feedback analysis
        params.status = 'Completed';
        break;
    }

    return params;
  };

  // Handle show report
  const handleShowReport = () => {
    const config: ReportConfig = {
      moduleId: activeTab,
      endpoint: ENDPOINTS[activeTab],
      params: getParams(),
      title: TITLES[activeTab],
      componentType: activeTab, // matches registry
      initialDateRange: dateRange, // Pass the selected date range to the modal
    };
    setModalConfig(config);
    setIsModalOpen(true);
  };

  // Handle modal close and clear date range
  const handleModalClose = () => {
    setIsModalOpen(false);
    setModalConfig(null);
    // Clear the date range after modal is closed
    setDateRange({ from: undefined, to: undefined });
  };

  return (
    <Screen>
      <div className="min-h-screen space-y-5">
        <div className="flex justify-between items-center px-8 py-6 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 bg-primary/10 rounded-xl flex items-center justify-center">
              <div className="w-6 h-6 bg-primary rounded-md"></div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Logistics Reports</h1>
              <p className="text-gray-600 mt-1">Generate and analyze logistics performance reports</p>
            </div>
          </div>
          <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Real-time data</span>
            </div>
          </div>
        </div>
        <div className="py-4 px-5">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as keyof typeof ENDPOINTS)} className="w-full">
            {/* Modern Responsive Tabs List */}
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-5 h-auto p-1 bg-muted/50 rounded-lg mb-6">
              {TABS.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <TabsTrigger
                    key={tab.key}
                    value={tab.key}
                    className="flex flex-col items-center gap-2 p-3 h-auto data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200 rounded-md"
                  >
                    <IconComponent className="h-4 w-4 md:h-5 md:w-5" />
                    <div className="text-center">
                      <div className="font-medium text-xs md:text-sm hidden sm:block">
                        {tab.title}
                      </div>
                      <div className="font-medium text-xs sm:hidden">
                        {tab.shortTitle}
                      </div>
                      <div className="text-xs text-muted-foreground hidden lg:block mt-1">
                        {tab.description}
                      </div>
                    </div>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Tab Content */}
            {TABS.map((tab) => (
              <TabsContent key={tab.key} value={tab.key} className="mt-0">
                <Card className="border-0 shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <tab.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg font-semibold text-gray-900">
                          {TITLES[tab.key as keyof typeof TITLES]}
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-500">
                          {tab.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <form
                      className="space-y-6"
                      onSubmit={(e) => {
                        e.preventDefault();
                        handleShowReport();
                      }}
                    >
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Date Range Selection
                          </label>
                          <div className="max-w-md">
                            <DateRangePicker
                              value={dateRange || { from: undefined, to: undefined }}
                              onChange={(range) => setDateRange(range || { from: undefined, to: undefined })}
                              fromYear={2000}
                              toYear={2100}
                            />
                          </div>
                        </div>
                        
                        {/* Date Range Display */}
                        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 text-sm">
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full ${dateRange?.from ? 'bg-primary' : 'bg-gray-300'}`}></div>
                              <span className="text-gray-600">From:</span>
                              <span className={`font-semibold ${dateRange?.from ? 'text-gray-900' : 'text-gray-500'}`}>
                                {dateRange?.from ? dateRange.from.toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric'
                                }) : "Not selected"}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full ${dateRange?.to ? 'bg-primary' : 'bg-gray-300'}`}></div>
                              <span className="text-gray-600">To:</span>
                              <span className={`font-semibold ${dateRange?.to ? 'text-gray-900' : 'text-gray-500'}`}>
                                {dateRange?.to ? dateRange.to.toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric'
                                }) : "Not selected"}
                              </span>
                            </div>
                          </div>
                          {dateRange?.from && dateRange?.to && (
                            <div className="mt-2 text-xs text-muted-foreground">
                              Duration: {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days
                            </div>
                          )}
                          {(!dateRange?.from && !dateRange?.to) && (
                            <div className="mt-2 text-xs text-gray-400 italic">
                              Please select a date range to generate the report
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
                        <OutlinedButton 
                          variant="primary" 
                          type="submit"
                          className="px-6 py-2.5 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                        >
                          <tab.icon className="h-4 w-4" />
                          Generate {tab.shortTitle} Report
                        </OutlinedButton>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </div>
        {/* Modal for reports */}
        {modalConfig && (
          <ReportsModal
            config={modalConfig}
            isOpen={isModalOpen}
            onClose={handleModalClose}
          />
        )}
      </div>
    </Screen>
  );
}
