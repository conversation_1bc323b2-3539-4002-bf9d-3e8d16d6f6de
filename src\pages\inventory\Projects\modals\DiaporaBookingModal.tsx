import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useState } from "react";
import { IconSwitch } from "@/components/custom/switch/IconSwitch";
import { Label } from "@/components/ui/label";
import Multiselect from "@/components/custom/forms/Multiselect";
import {
  useBookPlotMutation,
  useGetReserveDiasporaPlotQuery,
} from "@/redux/slices/projects";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useAuthHook } from "@/utils/useAuthHook";
import { CalendarIcon, FileText, Image } from "lucide-react";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import InputWithTags from "@/components/custom/forms/InputWithTags";
import { SingleDatePicker } from "@/components/custom/datepicker/DatePicker";
import { Popover, PopoverTrigger } from "@radix-ui/react-popover";
import { Calendar } from "@/components/ui/calendar";
import { PopoverContent } from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

type Props = {
  setSbModal: (e: boolean) => void;
  sbModal: boolean;
  bookingType: string;
  title: string;
};

const formSchema = z.object({
  client_id_pass: z
    .string()
    .min(1, { message: "ID/Passport number is required" })
    .trim(),
  first_name: z
    .string()
    .min(1, { message: "Customer first name is required" })
    .trim(),
  last_name: z
    .string()
    .min(1, { message: "Customer lsat name is required" })
    .trim(),
  phone: z
    .string()
    .min(1, { message: "Customer phone number is required" })
    .trim(),
  email: z.string().email({ message: "Invalid email address" }).trim(),
  state: z.string().min(1, { message: "State/Province is required" }).trim(),
  country: z.string().min(1, { message: "Country is required" }).trim(),
  amount: z.string().min(1, { message: "Amount is required" }).trim(),
  payment_mode: z
    .string()
    .min(1, { message: "Payment Mode is required" })
    .trim(),
  payments_of: z
    .string()
    .min(1, { message: "Payment description is required" })
    .trim(),
  marketer: z.string().min(1, { message: "Marketer name is required" }).trim(),
  description: z.string().min(1, { message: "Description is required" }).trim(),
  expected_payment_date: z.date({
    required_error: "Expected payment date is required",
  }),
});

const DiaporaBookingModal = ({
  setSbModal,
  sbModal,
  bookingType,
  title,
}: Props) => {
  const { user_details } = useAuthHook();
  const [bookSite, { isLoading: booking }] = useBookPlotMutation();
  const [fetchUsers, { data: usersList, isLoading: loadingUsers }] =
    useLazyGetUsersQuery();
  const { data: reservedPlots, isLoading: gettingPlots } =
    useGetReserveDiasporaPlotQuery({ marketer: user_details?.employee_no });

  const [plot, setPlot] = useState<string>("");
  const [reserved, setReserved] = useState<any>(null);

  useEffect(() => {
    if (reservedPlots?.data?.results?.length > 0) {
      let allPlots: any = [];
      const plots = reservedPlots?.data?.results;
      plots.map((plot: any) => {
        const plots = plot.plots.split(",").map((plot: string) => plot.trim());
        allPlots = [...allPlots, ...plots];
      });
      setReserved(allPlots);
    }
  }, [gettingPlots]);

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  const handleFileChange = (e: any) => {
    const selected = e.target.files[0];
    if (selected) {
      setFile(selected);
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setFile(null);
      setPreviewUrl(null);
      setIsPdf(false);
    }
  };

  // Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      client_id_pass: "",
      last_name: "",
      first_name: "",
      phone: "",
      email: "",
      state: "",
      country: "",
      amount: "",
      payments_of: "",
      marketer: "",
      description: "",
      payment_mode: "",
      expected_payment_date: new Date(),
    },
  });

  // submit handler.
  async function onSubmit(values: z.infer<typeof formSchema>): Promise<void> {
    if (!plot) {
      toast.error("Please select at least one plot");
      return;
    }

    const formData = {
      ...values,
      // diaspora_reservation:
      plots: plot,
      proof_of_payment: file,
      booking_type: bookingType,
    };

    try {
      const newFormData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            // value.forEach((item) => newFormData.append(key, item));
            newFormData.append(key, JSON.stringify(value));
          } else if (value instanceof Date) {
            newFormData.append(key, value.toISOString());
          } else {
            newFormData.append(key, value as string | Blob);
          }
        }
      });
      const res = await bookSite(newFormData).unwrap();
      if (res) {
        toast.success("Booking created successfully");
        setSbModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`${error?.error}`);
      }
      return;
    }
  }

  return (
    <BaseModal
      isOpen={sbModal}
      onOpenChange={setSbModal}
      size="xl"
      title={title}
      description="Fields with (*) are required"
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <div className="py-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="client_id_pass"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ID/Passport Number*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter ID/Passport Number"
                      className="border border-accent dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid md:grid-col-2 grid-col-1">
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Full Name"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Full Name"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Phone Number"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address*</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter Email"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>State/Province*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter State/Province"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Country"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <div className="space-y-2">
                <div className="flex gap-2 items-center flex-wrap">
                  <FormLabel>Reserved Plot Number *</FormLabel>
                  <select
                    value={plot}
                    onChange={(e) => setPlot(e.target.value)}
                    className="flex h-10 w-full rounded-md border border-accent dark:border-white/40 bg-background px-3 py-2 text-sm ring-offset-background"
                  >
                    <option value="">Select Plot</option>
                    {reserved &&
                      reserved?.map((plot: any) => (
                        <option key={plot} value={plot}>
                          {plot}
                        </option>
                      ))}
                  </select>
                </div>
              </div>

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount*</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter Amount"
                        className="border border-accent dark:border-white/40"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="payment_mode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Mode*</FormLabel>
                    <FormControl>
                      <select
                        {...field}
                        className="flex h-10 w-full rounded-md border border-accent dark:border-white/40 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Select Payment Mode</option>
                        <option value="CASH - N/A">CASH</option>
                        <option value="M-PESA - 921225">MPESA</option>
                        <option value="JP MORGAN CHASE - *********">
                          JP MORGAN CHASE
                        </option>
                        <option value="LOCAL BANK">LOCAL BANK</option>
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="payments_of"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment For*</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className="flex h-10 w-full rounded-md border border-accent dark:border-white/40 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="Deposit">Deposit</option>
                      <option value="Additional Deposit">
                        Additional Deposit
                      </option>
                      <option value="Installment">Installment</option>
                      <option value="Final Payment">Final Payment</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter Description"
                      className="border border-accent dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expected_payment_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Date*</FormLabel>
                  <div className="border border-accent dark:border-white/40  rounded-md">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal w-full",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="marketer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marketer*</FormLabel>
                  <div className="space-y-2 flex flex-col my-4">
                    <CustomSelectField
                      valueField="employee_no"
                      labelField="fullnames"
                      data={usersList?.data?.results}
                      queryFunc={fetchUsers}
                      setValue={(value) => field.onChange(value)}
                      loader={loadingUsers}
                      useSearchField={true}
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mx-auto">
              <FormLabel>Proof of Payment*</FormLabel>
              <label
                htmlFor="proof_file"
                className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
              >
                <input
                  id="proof_file"
                  type="file"
                  accept="image/*,application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />

                {previewUrl && !isPdf ? (
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-64 object-contain rounded-xl shadow-md"
                  />
                ) : file && isPdf ? (
                  <div className="flex flex-col items-center">
                    <FileText className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-700 font-medium">PDF Selected</p>
                  </div>
                ) : (
                  <>
                    <Image className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-500">
                      Click to upload image or PDF
                    </p>
                  </>
                )}
              </label>

              {file && (
                <p className="mt-2 text-center text-sm text-gray-600">
                  Selected file:{" "}
                  <span className="font-medium">{file.name}</span>
                </p>
              )}
            </div>

            <div className="w-full flex justify-end">
              {booking ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <Button type="submit" className="justify-end">
                  Submit
                </Button>
              )}
            </div>
          </form>
        </Form>
      </div>
    </BaseModal>
  );
};

export default DiaporaBookingModal;
