import React, { useMemo, useState, useEffect } from 'react';
import { CustomActiveShapePieChart } from '@/components/custom/charts/PieChartVariants';
import { SimpleLineChart } from '@/components/custom/charts/LineChartVariants';
import { CustomShapeBarChart } from '@/components/custom/charts/BarChartVariants';
import { Card6 } from '@/components/custom/cards/Card6';
import {
  Truck,
  MapPin,
  Car,
  Activity,
  CheckCircle,
  AlertCircle,
  Calendar,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  Zap,
  Eye,
  Edit,
  Key,
  User,
  Building,
  Trash2,
} from 'lucide-react';
import { Screen } from '@/app-components/layout/screen';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';

import {
  useGetSiteVisitsQuery,
  useGetSiteVisitQuery,
  useGetSiteVisitsSurveysQuery,
  useGetVehicleRequestsQuery,
  useGetVehiclesQuery,
  useGetSpecialBookingsQuery,
  useUpdateSiteVisitMutation,
  useGetDriversQuery,
  useUpdateVehicleRequestMutation,
  useDeleteSiteVisitMutation,
  VehicleRequest,
} from '@/redux/slices/logistics';
import CreateASpecialAssignment from './CreateSpecialAssignment';
import SpecialBookingsTable from './SpecialBookingsTable';
import { format } from 'date-fns';
import { toast } from '@/components/custom/Toast/MyToast';
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetUsersQuery, useGetDepartmentsQuery } from '@/redux/slices/user';
import { useGetProjectsQuery } from '@/redux/slices/projects';


// ———————————————————————————————————————————————————
//  SiteVisitModal (with "clients" fetched via surveys)
// ———————————————————————————————————————————————————
const SiteVisitModal = ({
  visit,
  onStatusUpdate,
  statusColors,
}: {
  visit: any;
  onStatusUpdate: (
    id: number,
    newStatus: string,
    driverId?: string,
    remarks?: string,
    visitObj?: any,
    vehicleId?: string
  ) => Promise<boolean>;
  statusColors: Record<string, string>;
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedDriverId, setSelectedDriverId] = useState<string>('');
  const [selectedVehicleId, setSelectedVehicleId] = useState<string>('');
  const [remarks, setRemarks] = useState<string>('');

  // Schedule editing states
  const [isEditingSchedule, setIsEditingSchedule] = useState(false);
  const [editedDate, setEditedDate] = useState<string>('');
  const [editedTime, setEditedTime] = useState<string>('');
  const [editedLocation, setEditedLocation] = useState<string>('');

  // 1) Fetch the detailed site visit record
  const { data: detailedVisit, isLoading: detailLoading } = useGetSiteVisitQuery(visit.id);

  // 2) Fetch "surveys" (clients) for this visit
  const {
    data: surveysResp,
    isLoading: surveysLoading,
    error: surveysError
  } = useGetSiteVisitsSurveysQuery({
    site_visit: visit.id,
    page: 1,
    page_size: 100,
  }, {
    skip: !visit.id // Skip query if no visit ID
  });

  // 3) Fetch drivers list
  const {
    data: driversResp,
    isLoading: driversLoading
  } = useGetDriversQuery({
    page: 1,
    page_size: 100,
  });

  // 4) Fetch vehicles list
  const {
    data: vehiclesResp,
    isLoading: vehiclesLoading
  } = useGetVehiclesQuery({
    page: 1,
    page_size: 100,
  });

  const drivers = driversResp?.data?.results || [];
  const vehicles = vehiclesResp?.data?.results || [];
  const visitData = detailedVisit || visit;

  // Debug logging for site visit data
  useEffect(() => {
    if (visitData) {
      console.log('=== Site Visit Data Debug ===');
      console.log('Visit ID:', visit.id);
      console.log('Visit Data marketer field:', visitData.marketer);
      console.log('Visit Data marketer type:', typeof visitData.marketer);
      console.log('Full visit data:', visitData);
    }
  }, [visitData, visit.id]);

  // Prefill driver, vehicle, schedule & remarks when detailedVisit arrives
  useEffect(() => {
    if (visitData.driver && typeof visitData.driver === 'object') {
      setSelectedDriverId(String(visitData.driver.id || ''));
    }
    if (visitData.vehicle && typeof visitData.vehicle === 'object') {
      setSelectedVehicleId(String(visitData.vehicle.id || ''));
    }
    if (visitData.remarks) {
      setRemarks(visitData.remarks);
    }

    // Initialize schedule editing fields
    if (visitData.pickup_date) {
      setEditedDate(visitData.pickup_date);
    }
    if (visitData.pickup_time) {
      setEditedTime(visitData.pickup_time);
    }
    if (visitData.pickup_location) {
      setEditedLocation(visitData.pickup_location);
    }
  }, [visitData.driver, visitData.vehicle, visitData.remarks, visitData.pickup_date, visitData.pickup_time, visitData.pickup_location]);

  // Map surveys → nested client objects, filtering out null/undefined clients
  // First try to get clients from surveys response, then fallback to direct site visit data
  const clients = useMemo(() => {
    // Try surveys response first
    const surveysClients = surveysResp?.data?.results
      ?.map((s) => s.site_visit_client)
      ?.filter((client) => client != null) || [];
    
    if (surveysClients.length > 0) {
      return surveysClients;
    }
    
    // Fallback to direct site visit client data
    const directClients = visitData?.site_visit_client || [];
    return Array.isArray(directClients) ? directClients : [];
  }, [surveysResp, visitData]);

  // Debug logging for client data
  useEffect(() => {
    console.log('=== Site Visit Client Debug ===');
    console.log('Visit ID:', visit.id);
    console.log('Visit Data:', visitData);
    console.log('Direct site_visit_client from visitData:', visitData?.site_visit_client);
    console.log('Surveys Response:', surveysResp);
    console.log('Surveys Results:', surveysResp?.data?.results);
    console.log('Surveys Loading:', surveysLoading);
    console.log('Surveys Error:', surveysError);
    if (surveysError) {
      console.log('Surveys Error Details:', {
        status: (surveysError as any)?.status,
        data: (surveysError as any)?.data,
        message: (surveysError as any)?.message
      });
    }
    console.log('Final extracted clients:', clients);
    console.log('Number of clients found:', clients.length);
    if (clients.length > 0) {
      console.log('Sample client data:', clients[0]);
      console.log('Client properties:', Object.keys(clients[0] || {}));
    }
    console.log('=== End Debug ===');
  }, [visit.id, visitData, surveysResp, surveysLoading, surveysError, clients]);

  // Add error state handling
  useEffect(() => {
    if (surveysError) {
      console.error('Failed to fetch surveys:', {
        error: surveysError,
        response: (surveysError as any)?.data,
        status: (surveysError as any)?.status,
        message: (surveysError as any)?.message
      });
      toast.error('Failed to load client details. Please try again.');
    }
  }, [surveysError]);

  const handleApprove = async () => {
    // Check if driver and vehicle assignment is required based on transport type
    const requiresAssignment = !visitData.is_self_drive &&
                              visitData.transport_type !== 'self_drive' &&
                              visitData.transport_type !== 'own_means' &&
                              visitData.pickup_location !== 'Self Drive' &&
                              visitData.pickup_location !== 'Own Means';

    if (requiresAssignment) {
      const driverAlreadyAssigned =
        visitData.driver && String(visitData.driver.id) === selectedDriverId;
      const vehicleAlreadyAssigned =
        visitData.vehicle && String(visitData.vehicle.id) === selectedVehicleId;

      // Check if driver is selected
      if (!driverAlreadyAssigned && !selectedDriverId) {
        toast.error('Cannot approve: No driver assigned. Please select a driver first.');
        return;
      }

      // Check if vehicle is selected
      if (!vehicleAlreadyAssigned && !selectedVehicleId) {
        toast.error('Cannot approve: No vehicle assigned. Please select a vehicle first.');
        return;
      }

      // Assign driver if not already assigned
      if (!driverAlreadyAssigned && selectedDriverId) {
        setIsUpdating(true);
        try {
          const okAssign = await onStatusUpdate(
            visit.id,
            'driver_assignment',
            selectedDriverId,
            undefined,
            visitData,
            selectedVehicleId
          );
          if (!okAssign) throw new Error();
        } catch {
          toast.error('Failed to assign driver. Please try again.');
          setIsUpdating(false);
          return;
        }
        setIsUpdating(false);
      }

      // Assign vehicle if not already assigned
      if (!vehicleAlreadyAssigned && selectedVehicleId) {
        setIsUpdating(true);
        try {
          const okAssign = await onStatusUpdate(
            visit.id,
            'vehicle_assignment',
            undefined,
            undefined,
            visitData,
            selectedVehicleId
          );
          if (!okAssign) throw new Error();
        } catch {
          toast.error('Failed to assign vehicle. Please try again.');
          setIsUpdating(false);
          return;
        }
        setIsUpdating(false);
      }
    }

    // Then update status → "Approved" (including remarks)
    setIsUpdating(true);
    try {
      const okStatus = await onStatusUpdate(visit.id, 'Approved', undefined, remarks, visitData, selectedVehicleId);
      if (okStatus) {
        toast.success('Site visit approved successfully!');
      } else {
        throw new Error();
      }
    } catch {
      toast.error('Failed to approve site visit. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleReject = async () => {
    setIsUpdating(true);
    try {
      const ok = await onStatusUpdate(visit.id, 'Rejected', undefined, remarks, visitData, selectedVehicleId);
      if (ok) {
        toast.success('Site visit rejected successfully!');
      } else {
        throw new Error();
      }
    } catch {
      toast.error('Failed to reject site visit. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleReview = async () => {
    setIsUpdating(true);
    try {
      const ok = await onStatusUpdate(visit.id, 'Reviewed', undefined, remarks, visitData, selectedVehicleId);
      if (ok) {
        toast.success('Site visit marked as reviewed successfully!');
      } else {
        throw new Error();
      }
    } catch {
      toast.error('Failed to mark site visit as reviewed. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Schedule editing functions
  const handleEditSchedule = () => {
    setIsEditingSchedule(true);
  };

  const handleCancelScheduleEdit = () => {
    setIsEditingSchedule(false);
    // Reset to original values
    setEditedDate(visitData.pickup_date || '');
    setEditedTime(visitData.pickup_time || '');
    setEditedLocation(visitData.pickup_location || '');
  };

  const handleSaveSchedule = async () => {
    if (!editedDate || !editedTime || !editedLocation) {
      toast.error('Please fill in all schedule fields');
      return;
    }

    setIsUpdating(true);
    try {
      // Create updated visit object with new schedule
      const updatedVisitData = {
        ...visitData,
        pickup_date: editedDate,
        pickup_time: editedTime,
        pickup_location: editedLocation
      };

      const ok = await onStatusUpdate(
        visit.id,
        'schedule_update',
        undefined,
        undefined,
        updatedVisitData,
        selectedVehicleId
      );

      if (ok) {
        toast.success('Schedule updated successfully!');
        setIsEditingSchedule(false);
      } else {
        throw new Error();
      }
    } catch {
      toast.error('Failed to update schedule. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (detailLoading || driversLoading || vehiclesLoading) {
    return (
      <DialogContent className="max-w-4xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center py-8">
          <Activity className="w-6 h-6 animate-spin mr-2" />
          <span>Loading site visit details...</span>
        </div>
      </DialogContent>
    );
  }

  return (
    <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 mx-2 sm:mx-4">
      <DialogHeader>
        <DialogTitle className="flex items-center text-lg sm:text-xl text-gray-900 dark:text-gray-100">
          <MapPin className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-blue-600 dark:text-blue-400" />
          Site Visit Details
        </DialogTitle>
        <DialogDescription className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Complete site visit information with client details and driver assignment
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 sm:space-y-6 py-2 sm:py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* ────────── Left Column ────────── */}
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                <MapPin className="w-4 h-4 mr-2" />
                Project Details
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700 dark:text-blue-300">Project:</span>
                  <span className="font-medium text-blue-900 dark:text-blue-100">
                    {String(
                      typeof visitData.project === 'object'
                        ? visitData.project?.name || 'N/A'
                        : visitData.project || 'N/A'
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700 dark:text-blue-300">Marketer:</span>
                  <span className="font-medium text-blue-900 dark:text-blue-100">
                    {String(
                      typeof visitData.marketer === 'object'
                        ? visitData.marketer?.fullnames ||
                          visitData.marketer?.name ||
                          'N/A'
                        : visitData.marketer || 'N/A'
                    )}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule
                </div>
                {!isEditingSchedule && visitData.status === 'Pending' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleEditSchedule}
                    className="text-green-700 border-green-300 hover:bg-green-100 dark:text-green-300 dark:border-green-600 dark:hover:bg-green-900/30"
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Edit
                  </Button>
                )}
              </h3>

              {!isEditingSchedule ? (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-green-700 dark:text-green-300">Date:</span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {format(new Date(visitData.pickup_date), 'MMMM dd, yyyy')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700 dark:text-green-300">Time:</span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {String(visitData.pickup_time || 'N/A')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700 dark:text-green-300">Location:</span>
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {String(visitData.pickup_location || 'N/A')}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-green-700 dark:text-green-300">
                      Date
                    </Label>
                    <Input
                      type="date"
                      value={editedDate}
                      onChange={(e) => setEditedDate(e.target.value)}
                      className="border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-green-700 dark:text-green-300">
                      Time
                    </Label>
                    <Input
                      type="time"
                      value={editedTime}
                      onChange={(e) => setEditedTime(e.target.value)}
                      className="border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-green-700 dark:text-green-300">
                      Location
                    </Label>
                    <Input
                      type="text"
                      value={editedLocation}
                      onChange={(e) => setEditedLocation(e.target.value)}
                      placeholder="Enter pickup location"
                      className="border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400"
                    />
                  </div>
                  <div className="flex gap-2 pt-2">
                    <Button
                      size="sm"
                      onClick={handleSaveSchedule}
                      disabled={isUpdating}
                      className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white"
                    >
                      {isUpdating ? (
                        <Activity className="w-3 h-3 mr-1 animate-spin" />
                      ) : (
                        <CheckCircle className="w-3 h-3 mr-1" />
                      )}
                      Save
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelScheduleEdit}
                      disabled={isUpdating}
                      className="text-gray-600 border-gray-300 hover:bg-gray-100 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-800"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* ────────── Middle Column ────────── */}
          <div className="space-y-4">
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                <Car className="w-4 h-4 mr-2" />
                Transport & Driver Assignment
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-purple-700 dark:text-purple-300">Transport Type:</span>
                  <div className="flex items-center space-x-2">
                    {visitData.transport_type === 'self_drive' || visitData.pickup_location === 'Self Drive' ? (
                      <>
                        <Key className="w-4 h-4 text-green-600" />
                        <span className="font-medium text-purple-900 dark:text-purple-100">Self Drive (Staff Vehicle)</span>
                      </>
                    ) : visitData.transport_type === 'own_means' || visitData.pickup_location === 'Own Means' ? (
                      <>
                        <User className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-purple-900 dark:text-purple-100">Own Means (Client Vehicle)</span>
                      </>
                    ) : visitData.transport_type === 'outsourced' ? (
                      <>
                        <Truck className="w-4 h-4 text-orange-600" />
                        <span className="font-medium text-purple-900 dark:text-purple-100">Outsourced</span>
                      </>
                    ) : (
                      <>
                        <Car className="w-4 h-4 text-purple-600" />
                        <span className="font-medium text-purple-900 dark:text-purple-100">Company Vehicle</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Only show driver and vehicle assignment for company vehicles */}
                {!visitData.is_self_drive &&
                 visitData.transport_type !== 'self_drive' &&
                 visitData.transport_type !== 'own_means' &&
                 visitData.pickup_location !== 'Self Drive' &&
                 visitData.pickup_location !== 'Own Means' && (
                  <div className="space-y-4">
                    {/* Driver Selection */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="driver-select"
                        className="text-sm font-medium text-purple-700 dark:text-purple-300"
                      >
                        Select Driver
                      </Label>
                      <Select
                        value={selectedDriverId}
                        onValueChange={setSelectedDriverId}
                      >
                        <SelectTrigger className="border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400">
                          <SelectValue placeholder="Select a driver..." />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                          {drivers.map((driver: any) => (
                            <SelectItem key={driver.id} value={String(driver.id)}>
                              {driver.fullnames ||
                                `${driver.first_name} ${driver.last_name}` ||
                                driver.username}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {visitData.driver && (
                        <div className="text-xs text-purple-600 dark:text-purple-400">
                          Current: {String(
                            typeof visitData.driver === 'object'
                              ? visitData.driver?.fullnames ||
                                visitData.driver?.name ||
                                'Unknown'
                              : visitData.driver || 'Unknown'
                          )}
                        </div>
                      )}
                    </div>

                    {/* Vehicle Selection */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="vehicle-select"
                        className="text-sm font-medium text-purple-700 dark:text-purple-300"
                      >
                        Select Vehicle
                      </Label>
                      <Select
                        value={selectedVehicleId}
                        onValueChange={setSelectedVehicleId}
                      >
                        <SelectTrigger className="border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400">
                          <SelectValue placeholder="Select a vehicle..." />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                          {vehicles
                            .filter((vehicle: any) => vehicle.status?.toLowerCase() === 'available')
                            .length > 0 ? (
                            vehicles
                              .filter((vehicle: any) => vehicle.status?.toLowerCase() === 'available')
                              .map((vehicle: any) => (
                                <SelectItem key={vehicle.id} value={String(vehicle.id)}>
                                  <div className="flex items-center space-x-2">
                                    <Car className="w-4 h-4 text-purple-600" />
                                    <span>
                                      {vehicle.make || 'Unknown'} {vehicle.model || ''} - {vehicle.vehicle_registration}
                                    </span>
                                    <div className="flex items-center space-x-1 ml-2">
                                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                      <span className="text-xs text-green-600">Available</span>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))
                          ) : (
                            <SelectItem value="no-vehicles" disabled>
                              <div className="flex items-center space-x-2 text-gray-500">
                                <Car className="w-4 h-4" />
                                <span>No available vehicles</span>
                              </div>
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      {visitData.vehicle && (
                        <div className="text-xs text-purple-600 dark:text-purple-400">
                          Current: {String(
                            typeof visitData.vehicle === 'object'
                              ? `${visitData.vehicle?.make} ${visitData.vehicle?.model} - ${visitData.vehicle?.vehicle_registration}` ||
                                'Unknown Vehicle'
                              : visitData.vehicle || 'Unknown'
                          )}
                        </div>
                      )}
                      <div className="text-xs text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
                        🚗 Only available vehicles are shown. Vehicle assignment prevents double-booking.
                      </div>
                    </div>
                  </div>
                )}

                {/* Show message for transport types that don't need drivers */}
                {(visitData.transport_type === 'self_drive' ||
                  visitData.transport_type === 'own_means' ||
                  visitData.pickup_location === 'Self Drive' ||
                  visitData.pickup_location === 'Own Means') && (
                  <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg">
                    <div className="text-xs text-blue-700 dark:text-blue-300">
                      ℹ️ No driver assignment required for this transport type
                    </div>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-purple-700 dark:text-purple-300">Current Status:</span>
                  <Badge
                    className={`${statusColors[
                      visitData.status as keyof typeof statusColors
                    ]} border-0`}
                  >
                    {String(visitData.status || 'Unknown')}
                  </Badge>
                </div>
              </div>
            </div>

            {visitData.status === 'Pending' && (
              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                <h3 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
                  <Edit className="w-4 h-4 mr-2" />
                  Actions
                </h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      onClick={handleApprove}
                      disabled={isUpdating}
                      className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white"
                    >
                      {isUpdating ? (
                        <Activity className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <CheckCircle className="w-4 h-4 mr-2" />
                      )}
                      Approve
                    </Button>
                    <Button
                      onClick={handleReject}
                      disabled={isUpdating}
                      variant="destructive"
                      className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600"
                    >
                      {isUpdating ? (
                        <Activity className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <AlertCircle className="w-4 h-4 mr-2" />
                      )}
                      Reject
                    </Button>
                  </div>
                  {/* Show warning only for transport types that require drivers and vehicles */}
                  {!visitData.is_self_drive &&
                    visitData.transport_type !== 'self_drive' &&
                    visitData.transport_type !== 'own_means' &&
                    visitData.pickup_location !== 'Self Drive' &&
                    visitData.pickup_location !== 'Own Means' && (
                      <div className="space-y-2">
                        {(!visitData.driver && !selectedDriverId) && (
                          <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 p-2 rounded">
                            ⚠️ Cannot approve without driver assignment. Please select a driver above.
                          </div>
                        )}
                        {(!visitData.vehicle && !selectedVehicleId) && (
                          <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 p-2 rounded">
                            ⚠️ Cannot approve without vehicle assignment. Please select a vehicle above.
                          </div>
                        )}
                      </div>
                    )}
                </div>
              </div>
            )}
          </div>

          {/* ────────── Right Column ────────── */}
          <div className="space-y-4">
            <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-3 flex items-center">
                <Activity className="w-4 h-4 mr-2" />
                Clients ({clients.length})
                {surveysLoading && (
                  <Activity className="w-4 h-4 animate-spin ml-2 text-indigo-600" />
                )}
              </h3>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {surveysLoading ? (
                  <div className="text-sm text-indigo-600 dark:text-indigo-400 text-center py-4">
                    <Activity className="w-4 h-4 animate-spin mx-auto mb-2" />
                    Loading client details...
                  </div>
                ) : surveysError ? (
                  <div className="text-sm text-red-600 dark:text-red-400 text-center py-4">
                    Failed to load client details
                  </div>
                ) : clients.length > 0 ? (
                  clients.map((client: any, idx: number) => {
                    // Additional safety check to ensure client is not null/undefined
                    if (!client) return null;

                    return (
                      <div
                        key={idx}
                        className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-indigo-200 dark:border-indigo-700"
                      >
                        <div className="space-y-1 text-sm">
                          <div className="font-medium text-indigo-900 dark:text-indigo-100">
                            {String(client.name || client.firstName || 'N/A')} {String(client.lastName || '')}
                          </div>
                          <div className="text-indigo-600 dark:text-indigo-400 text-xs font-medium uppercase">
                            {client.client_type === 'representative' ? '👤 Representative' : '🏢 Client'}
                          </div>
                          <div className="text-indigo-700 dark:text-indigo-300">
                            📞 {String(client.phone_number || client.phone || 'N/A')}
                          </div>
                          {client.email && (
                            <div className="text-indigo-700 dark:text-indigo-300">
                              ✉️ {String(client.email)}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  }).filter(Boolean)
                ) : (
                  <div className="text-sm text-indigo-600 dark:text-indigo-400 text-center py-4">
                    No clients assigned to this visit
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Remarks
              </h3>
              <Textarea
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                placeholder="Add any remarks or notes about this site visit..."
                className="min-h-[100px] resize-none border-gray-200 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500"
              />
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Changes will be saved when you approve or update the visit
              </div>
            </div>
          </div>
        </div>
      </div>
    </DialogContent>
  );
};


// ———————————————————————————————————————————————————
//  Stats (dashboard) component
// ———————————————————————————————————————————————————
export default function Stats() {
  // —————————————————— Status Color Definitions ——————————————————
  const statusColors: Record<string, string> = {
    Pending:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    Approved:
      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'In Progress':
      'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    Completed:
      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    Cancelled:
      'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    Rejected:
      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    Reviewed:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  };

  // Enhanced status colors for full cell background
  const statusCellColors: Record<string, string> = {
    Pending:
      'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700',
    Approved:
      'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700',
    'In Progress':
      'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-700',
    Completed:
      'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700',
    Cancelled:
      'bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-700',
    Rejected:
      'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700',
    Reviewed:
      'bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-700',
  };

  // —————————————————— State + Hooks ——————————————————
  const [updateSiteVisit] = useUpdateSiteVisitMutation();
  const [updateVehicleRequest] = useUpdateVehicleRequestMutation();
  const [deleteSiteVisit] = useDeleteSiteVisitMutation();
  const { refetch: refetchSiteVisits } = useGetSiteVisitsQuery({ page: 1, page_size: 100 });
  const { refetch: refetchVehicleRequests } = useGetVehicleRequestsQuery({ page: 1, page_size: 100 });

  // Get current user
  const { user_details } = useAuthHook();
  const currentUserEmployeeNo = user_details?.employee_no;

  // Fetch all employees for name resolution - use high page_size to get all employees
  const { data: employeesResp, isLoading: employeesLoading } = useGetUsersQuery({
    page_size: 2000, // Fetch up to 2000 employees to ensure we get all
    ordering: "-id", // Order by newest first
  });

  // Extract employees from response
  const employees = useMemo(() => {
    if (!employeesResp) return [];

    // Handle different response formats
    if (Array.isArray(employeesResp)) {
      return employeesResp;
    } else if ((employeesResp as any).data?.results) {
      return (employeesResp as any).data.results;
    } else if ((employeesResp as any).results) {
      return (employeesResp as any).results;
    }

    return [];
  }, [employeesResp]);

  // Log final employee count for debugging
  useEffect(() => {
    if (employees && employees.length > 0) {
      console.log(`Total employees loaded: ${employees.length}`);
      console.log('Sample employees:', employees.slice(0, 3).map((e: any) => ({
        employee_no: e.employee_no,
        fullnames: e.fullnames
      })));
    }
  }, [employees]);

  // Fetch departments for special booking assignment resolution
  const { data: departmentsResp } = useGetDepartmentsQuery({
    page: 1,
    page_size: 100,
  });
  const departments = Array.isArray(departmentsResp) ? departmentsResp : [];

  // Fetch projects for project name to ID resolution
  const { data: projectsResp, isLoading: projectsLoading } = useGetProjectsQuery({
    page: 1,
    page_size: 1000, // Increase to get all projects
  });

  // Handle different response formats for projects
  const projects = useMemo(() => {
    console.log('Processing projects response:', {
      projectsResp,
      hasResults: !!projectsResp?.results,
      hasData: !!projectsResp?.data,
      hasDataResults: !!projectsResp?.data?.results,
      keys: projectsResp ? Object.keys(projectsResp) : []
    });

    if (!projectsResp) return [];

    // Check if it's directly under results
    if (projectsResp.results && Array.isArray(projectsResp.results)) {
      console.log('Using results format, length:', projectsResp.results.length);
      return projectsResp.results;
    }

    // Check if it's nested under data.results
    if (projectsResp.data?.results && Array.isArray(projectsResp.data.results)) {
      console.log('Using data.results format, length:', projectsResp.data.results.length);
      return projectsResp.data.results;
    }

    // Check if it's a direct array
    if (Array.isArray(projectsResp)) {
      console.log('Using direct array format, length:', projectsResp.length);
      return projectsResp;
    }

    console.warn('Unexpected projects response format:', projectsResp);
    return [];
  }, [projectsResp]);

  // Add states for tabs and pagination
  const [selectedStatus, setSelectedStatus] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(5);

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [visitToDelete, setVisitToDelete] = useState<{ id: number; title: string } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Vehicle Request Approval Modal State
  const [isVehicleRequestModalOpen, setIsVehicleRequestModalOpen] = useState(false);
  const [selectedVehicleRequest, setSelectedVehicleRequest] = useState<VehicleRequest | null>(null);
  const [selectedVehicleId, setSelectedVehicleId] = useState<string>('');
  const [selectedDriverId, setSelectedDriverId] = useState<string>('');
  const [approvalRemarks, setApprovalRemarks] = useState<string>('');
  const [isApprovingRequest, setIsApprovingRequest] = useState(false);

  // Fetch drivers list at component level
  const {
    data: driversResp,
    isLoading: driversLoading
  } = useGetDriversQuery({
    page: 1,
    page_size: 1000, // Increase to get all drivers
  });

  // Handle different response formats for drivers
  const drivers = useMemo(() => {
    console.log('Processing drivers response:', {
      driversResp,
      hasData: !!driversResp?.data,
      hasDataResults: !!driversResp?.data?.results,
      hasResults: !!driversResp?.results,
      keys: driversResp ? Object.keys(driversResp) : []
    });

    if (!driversResp) return [];

    // Check if it's nested under data.results (most common format)
    if (driversResp.data?.results && Array.isArray(driversResp.data.results)) {
      console.log('Using data.results format, length:', driversResp.data.results.length);
      return driversResp.data.results;
    }

    // Check if it's directly under results
    if (driversResp.results && Array.isArray(driversResp.results)) {
      console.log('Using results format, length:', driversResp.results.length);
      return driversResp.results;
    }

    // Check if it's a direct array
    if (Array.isArray(driversResp)) {
      console.log('Using direct array format, length:', driversResp.length);
      return driversResp;
    }

    console.warn('Unexpected drivers response format:', driversResp);
    return [];
  }, [driversResp]);

  // Vehicle Request Approval Functions
  const handleVehicleRequestApproval = (request: VehicleRequest) => {
    setSelectedVehicleRequest(request);
    setSelectedVehicleId('');
    setSelectedDriverId('');
    setApprovalRemarks('');
    setIsVehicleRequestModalOpen(true);
  };

  // Vehicle availability checking function - vehicles cannot be double-booked
  const checkVehicleAvailability = (vehicleId: string, requestDate: string, requestTime: string) => {
    // Check if vehicle is already booked for the same date/time
    // Note: Drivers can handle multiple trips, but vehicles cannot be assigned to multiple trips simultaneously
    const conflictingRequests = (vehicleRequestsResp?.data?.results || []).filter((request: any) =>
      request.vehicle === parseInt(vehicleId, 10) &&
      request.pickup_date === requestDate &&
      request.pickup_time === requestTime &&
      ['Approved', 'In Progress'].includes(request.status)
    );

    const conflictingSiteVisits = (visitsResp?.data?.results || []).filter((visit: any) =>
      visit.vehicle === parseInt(vehicleId, 10) &&
      visit.pickup_date === requestDate &&
      visit.pickup_time === requestTime &&
      ['Approved', 'In Progress'].includes(visit.status)
    );

    return conflictingRequests.length === 0 && conflictingSiteVisits.length === 0;
  };

  // Get driver's current assignments for informational purposes
  const getDriverAssignments = (driverId: string, requestDate: string) => {
    const driverRequests = (vehicleRequestsResp?.data?.results || []).filter((request: any) =>
      request.driver === driverId &&
      request.pickup_date === requestDate &&
      ['Approved', 'In Progress'].includes(request.status)
    );

    const driverSiteVisits = (visitsResp?.data?.results || []).filter((visit: any) =>
      visit.driver === driverId &&
      visit.pickup_date === requestDate &&
      ['Approved', 'In Progress'].includes(visit.status)
    );

    return [...driverRequests, ...driverSiteVisits];
  };

  const handleApproveVehicleRequest = async () => {
    if (!selectedVehicleRequest || !selectedVehicleId || !selectedDriverId) {
      toast.error('Please select both vehicle and driver before approving');
      return;
    }

    // Check vehicle availability for double-booking prevention
    const isVehicleAvailable = checkVehicleAvailability(
      selectedVehicleId,
      selectedVehicleRequest.pickup_date,
      selectedVehicleRequest.pickup_time
    );

    if (!isVehicleAvailable) {
      toast.error('🚫 Vehicle Double-Booking Prevention: This vehicle is already assigned to another trip at the same date/time. Please select a different vehicle.');
      return;
    }

    setIsApprovingRequest(true);
    try {
      // Find the selected driver to get their employee_no
      const selectedDriver = drivers.find((d: any) => d.id === parseInt(selectedDriverId, 10));

      if (!selectedDriver) {
        toast.error('Selected driver not found. Please try again.');
        return;
      }

      if (!selectedDriver.employee_no) {
        toast.error('Selected driver has no employee number. Please contact support.');
        return;
      }

      const updateData = {
        id: selectedVehicleRequest.id!,
        status: 'Approved' as const,
        vehicle: parseInt(selectedVehicleId, 10),
        driver: selectedDriver.employee_no,
        remarks: approvalRemarks || undefined,
      };

      console.log('Approving vehicle request with data:', updateData);

      const result = await updateVehicleRequest(updateData).unwrap();
      console.log('Vehicle request approval result:', result);

      toast.success(`✅ Vehicle request approved! Vehicle exclusively assigned for this time slot. Driver can handle additional trips if needed.`);

      // Refetch data to update the UI
      await refetchVehicleRequests();

      // Close modal and reset state
      setIsVehicleRequestModalOpen(false);
      setSelectedVehicleRequest(null);
      setSelectedVehicleId('');
      setSelectedDriverId('');
      setApprovalRemarks('');

    } catch (error: any) {
      console.error('Failed to approve vehicle request:', error);
      const errorMessage = error?.data?.detail || error?.message || 'Failed to approve vehicle request';
      toast.error(`❌ Failed to approve request: ${errorMessage}`);
    } finally {
      setIsApprovingRequest(false);
    }
  };

  const handleRejectVehicleRequest = async () => {
    if (!selectedVehicleRequest) return;

    setIsApprovingRequest(true);
    try {
      const updateData = {
        id: selectedVehicleRequest.id!,
        status: 'Rejected' as const,
        remarks: approvalRemarks || 'Request rejected',
      };

      console.log('Rejecting vehicle request with data:', updateData);

      const result = await updateVehicleRequest(updateData).unwrap();
      console.log('Vehicle request rejection result:', result);

      toast.success(`❌ Vehicle request rejected.`);

      // Refetch data to update the UI
      await refetchVehicleRequests();

      // Close modal and reset state
      setIsVehicleRequestModalOpen(false);
      setSelectedVehicleRequest(null);
      setSelectedVehicleId('');
      setSelectedDriverId('');
      setApprovalRemarks('');

    } catch (error: any) {
      console.error('Failed to reject vehicle request:', error);
      const errorMessage = error?.data?.detail || error?.message || 'Failed to reject vehicle request';
      toast.error(`❌ Failed to reject request: ${errorMessage}`);
    } finally {
      setIsApprovingRequest(false);
    }
  };

  // Helper function to validate and resolve IDs
  const validateAndResolveIds = (visitObj: any) => {
    const resolvedData: any = {};

    // Resolve marketer (any employee can create a site visit)
    if (visitObj.marketer) {
      console.log('Resolving marketer:', {
        marketer: visitObj.marketer,
        type: typeof visitObj.marketer,
        isObject: typeof visitObj.marketer === 'object',
        hasEmployeeNo: typeof visitObj.marketer === 'object' && visitObj.marketer?.employee_no
      });

      if (typeof visitObj.marketer === 'object' && visitObj.marketer.employee_no) {
        resolvedData.marketer = visitObj.marketer.employee_no;
      } else {
        const employeeStr = String(visitObj.marketer);

        // First, check if the marketer field already contains a valid employee number
        const foundByEmployeeNo = employees.find((emp: any) => emp.employee_no === employeeStr);

        if (foundByEmployeeNo?.employee_no) {
          resolvedData.marketer = foundByEmployeeNo.employee_no;
        } else {
          // If not found by employee number, try to find by name variations
          let foundByName = employees.find((emp: any) =>
            emp.fullnames === employeeStr ||
            `${emp.first_name} ${emp.last_name}` === employeeStr ||
            emp.username === employeeStr
          );

          // If exact match not found, try partial/fuzzy matching
          if (!foundByName) {
            const searchTermLower = employeeStr.toLowerCase().trim();

            // First try partial matching
            foundByName = employees.find((emp: any) => {
              const fullnamesLower = (emp.fullnames || '').toLowerCase();
              const firstLastLower = `${emp.first_name || ''} ${emp.last_name || ''}`.toLowerCase().trim();
              const usernameLower = (emp.username || '').toLowerCase();

              // Check if search term contains the employee's name or vice versa
              return fullnamesLower.includes(searchTermLower) ||
                     searchTermLower.includes(fullnamesLower) ||
                     firstLastLower.includes(searchTermLower) ||
                     searchTermLower.includes(firstLastLower) ||
                     usernameLower.includes(searchTermLower) ||
                     searchTermLower.includes(usernameLower);
            });

            // If still not found, try word-by-word matching for names with multiple words
            if (!foundByName) {
              const searchWords = searchTermLower.split(/\s+/).filter(word => word.length > 2);
              if (searchWords.length >= 2) {
                foundByName = employees.find((emp: any) => {
                  const fullnamesLower = (emp.fullnames || '').toLowerCase();
                  const firstLastLower = `${emp.first_name || ''} ${emp.last_name || ''}`.toLowerCase().trim();

                  // Check if at least 2 words from search term are found in employee name
                  const matchingWords = searchWords.filter(word =>
                    fullnamesLower.includes(word) || firstLastLower.includes(word)
                  );

                  return matchingWords.length >= 2;
                });
              }
            }
          }

          if (foundByName?.employee_no) {
            resolvedData.marketer = foundByName.employee_no;
            console.log(`Found employee by fuzzy match: "${employeeStr}" -> "${foundByName.fullnames}" (${foundByName.employee_no})`);
          } else {
            // Enhanced debugging for employee lookup failure
            console.error('Employee lookup failed:', {
              searchTerm: employeeStr,
              searchType: typeof visitObj.marketer,
              totalEmployeesSearched: employees.length
            });

            // Find similar names to suggest
            const searchTermLower = employeeStr.toLowerCase().trim();
            const searchWords = searchTermLower.split(/\s+/).filter(word => word.length > 2);

            const similarEmployees = employees.filter((emp: any) => {
              const fullnamesLower = (emp.fullnames || '').toLowerCase();
              const firstLastLower = `${emp.first_name || ''} ${emp.last_name || ''}`.toLowerCase().trim();

              // Find employees with at least one matching word
              return searchWords.some(word =>
                fullnamesLower.includes(word) || firstLastLower.includes(word)
              );
            }).slice(0, 5); // Limit to 5 suggestions

            // Instead of throwing an error, let's try a more lenient approach
            // Check if the marketer string looks like an employee number pattern
            if (/^[A-Z]{2}\/[A-Z]{2}\/\d{3}$/.test(employeeStr)) {
              // It looks like an employee number but wasn't found - use it as is
              console.warn(`Employee number "${employeeStr}" not found in employees list, but using it anyway as it matches the pattern`);
              resolvedData.marketer = employeeStr;
            } else {
              // For better user experience, let's be more lenient
              // Instead of throwing an error, we'll log a warning and continue
              console.warn(`Employee "${employeeStr}" not found, but continuing with the operation.`);

              if (similarEmployees.length > 0) {
                const suggestions = similarEmployees.map((e: any) => e.fullnames || e.username).join(', ');
                console.warn(`Similar employees found: ${suggestions}`);

                // Optionally, you could auto-select the first similar employee
                // For now, we'll just log the suggestion and continue without setting marketer
                // resolvedData.marketer = similarEmployees[0].employee_no;
              }

              // Don't set marketer field if employee not found - let the backend handle it
              // This prevents the entire operation from failing due to a missing employee
              console.warn(`Skipping marketer assignment for "${employeeStr}" - employee not found in local cache`);

              // Optionally throw error if you want strict validation
              // Uncomment the lines below to restore strict validation:
              /*
              let errorMessage = `Employee "${employeeStr}" not found.`;
              if (similarEmployees.length > 0) {
                const suggestions = similarEmployees.map((e: any) => e.fullnames || e.username).join(', ');
                errorMessage += ` Did you mean one of these: ${suggestions}?`;
              }
              throw new Error(errorMessage);
              */
            }
          }
        }
      }
    }

    // Resolve project
    if (visitObj.project) {
      if (typeof visitObj.project === 'object' && visitObj.project.projectId) {
        resolvedData.project = visitObj.project.projectId;
      } else {
        const projectStr = String(visitObj.project);
        const foundProject = projects.find((proj: any) =>
          proj.name === projectStr ||
          proj.projectId === projectStr ||
          proj.initials === projectStr
        );

        if (foundProject?.projectId) {
          resolvedData.project = foundProject.projectId;
        } else {
          throw new Error(`Project "${projectStr}" not found. Available projects: ${projects.map((p: any) => p.name).join(', ')}`);
        }
      }
    }

    // Resolve driver
    if (visitObj.driver) {
      if (typeof visitObj.driver === 'object' && visitObj.driver.employee_no) {
        resolvedData.driver = visitObj.driver.employee_no;
      } else {
        const driverStr = String(visitObj.driver);
        const foundDriver = drivers.find((driver: any) =>
          driver.employee_no === driverStr ||
          driver.fullnames === driverStr ||
          `${driver.first_name} ${driver.last_name}` === driverStr ||
          driver.username === driverStr
        );

        if (foundDriver?.employee_no) {
          resolvedData.driver = foundDriver.employee_no;
        } else {
          throw new Error(`Driver "${driverStr}" not found. Available drivers: ${drivers.map((d: any) => d.fullnames || d.username).join(', ')}`);
        }
      }
    }

    return resolvedData;
  };

  const handleStatusUpdate = async (
    visitId: number,
    newStatus: string,
    driverId?: string,
    remarks?: string,
    visitObj?: any,
    vehicleId?: string
  ) => {
    try {
      const patchData: any = {};

      // Always send all required fields for approval
      if (newStatus === 'Approved' && visitObj) {
        // Validate that we have the required reference data
        if (employeesLoading) {
          throw new Error('Employee data is still loading. Please wait a moment and try again.');
        }
        if (!employees || employees.length === 0) {
          console.error('Employee data validation failed:', {
            employeesResp,
            employees,
            employeesLength: employees?.length,
            isArray: Array.isArray(employees)
          });
          throw new Error('Employee data not loaded. Please refresh the page and try again.');
        }
        if (projectsLoading) {
          throw new Error('Project data is still loading. Please wait a moment and try again.');
        }
        if (!projects || projects.length === 0) {
          console.error('Project data validation failed:', {
            projectsResp,
            projects,
            projectsLength: projects?.length
          });
          throw new Error('Project data not loaded. Please refresh the page and try again.');
        }
        if (driversLoading) {
          throw new Error('Driver data is still loading. Please wait a moment and try again.');
        }
        if (!drivers || drivers.length === 0) {
          console.error('Driver data validation failed:', {
            driversResp,
            drivers,
            driversLength: drivers?.length
          });
          throw new Error('Driver data not loaded. Please refresh the page and try again.');
        }
        // Basic fields
        patchData.pickup_time = visitObj.pickup_time;
        patchData.pickup_date = visitObj.pickup_date;
        patchData.pickup_location = visitObj.pickup_location;
        patchData.status = newStatus;

        // Use the helper function to resolve all IDs
        const resolvedIds = validateAndResolveIds(visitObj);

        // Exclude marketer from patch data to preserve existing marketer assignment
        // This prevents the marketer field from being replaced during approval
        const { marketer, ...idsWithoutMarketer } = resolvedIds;
        Object.assign(patchData, idsWithoutMarketer);

        // Handle vehicle - use vehicleId if provided, otherwise use existing vehicle
        if (vehicleId) {
          patchData.vehicle = parseInt(vehicleId, 10);
        } else if (visitObj.vehicle) {
          patchData.vehicle = typeof visitObj.vehicle === 'object' ? visitObj.vehicle.id : visitObj.vehicle;
        }

        // Handle remarks
        if (remarks !== undefined) {
          patchData.remarks = remarks;
        }

        // Log the approval attempt with detailed debugging
        console.log('Attempting site visit approval:', {
          visitId,
          patchData: {
            marketer: 'EXCLUDED (preserving existing)',
            project: patchData.project,
            driver: patchData.driver,
            vehicle: patchData.vehicle,
            status: patchData.status,
            pickup_time: patchData.pickup_time,
            pickup_date: patchData.pickup_date,
            pickup_location: patchData.pickup_location,
            remarks: patchData.remarks
          },
          originalData: {
            marketer: visitObj.marketer,
            project: visitObj.project,
            driver: visitObj.driver,
            vehicle: visitObj.vehicle
          },
          availableData: {
            employeesCount: employees.length,
            projectsCount: projects.length,
            driversCount: drivers.length,
            sampleEmployee: employees[0],
            sampleProject: projects[0],
            sampleDriver: drivers[0]
          },
          dataLoadingStates: {
            employeesLoading,
            driversLoading,
            projectsLoading
          }
        });
      } else if (newStatus === 'driver_assignment' && driverId) {
        // Find the selected driver to get their employee_no
        const selectedDriver = drivers.find((d: any) => d.id === parseInt(driverId, 10));

        if (!selectedDriver) {
          toast.error('Selected driver not found. Please try again.');
          return false;
        }

        if (!selectedDriver.employee_no) {
          toast.error('Selected driver has no employee number. Please contact support.');
          return false;
        }

        // Use employee_no instead of id
        patchData.driver = selectedDriver.employee_no;

        // Log the driver assignment attempt
        console.log('Attempting driver assignment:', {
          visitId,
          driverId: selectedDriver.id,
          employeeNo: selectedDriver.employee_no,
          driverName: selectedDriver.fullnames || `${selectedDriver.first_name} ${selectedDriver.last_name}`
        });
      } else if (newStatus === 'vehicle_assignment' && vehicleId) {
        // Find the selected vehicle to get its details from the main component's vehicles
        const mainVehicles = vehiclesResp?.data?.results || [];
        const selectedVehicle = mainVehicles.find((v: any) => v.id === parseInt(vehicleId, 10));

        if (!selectedVehicle) {
          toast.error('Selected vehicle not found. Please try again.');
          return false;
        }

        if (selectedVehicle.status?.toLowerCase() !== 'available') {
          toast.error('Selected vehicle is not available. Please choose another vehicle.');
          return false;
        }

        // Use vehicle id
        patchData.vehicle = selectedVehicle.id;

        // Log the vehicle assignment attempt
        console.log('Attempting vehicle assignment:', {
          visitId,
          vehicleId: selectedVehicle.id,
          vehicleName: `${selectedVehicle.make} ${selectedVehicle.model} - ${selectedVehicle.vehicle_registration}`,
          vehicleStatus: selectedVehicle.status
        });
      } else if (newStatus === 'schedule_update' && visitObj) {
        // Update schedule fields
        patchData.pickup_date = visitObj.pickup_date;
        patchData.pickup_time = visitObj.pickup_time;
        patchData.pickup_location = visitObj.pickup_location;

        // Log the schedule update attempt
        console.log('Attempting schedule update:', {
          visitId,
          newDate: visitObj.pickup_date,
          newTime: visitObj.pickup_time,
          newLocation: visitObj.pickup_location
        });
      } else {
        patchData.status = newStatus;
        if (remarks !== undefined) patchData.remarks = remarks;
      }

      // Log the request payload for debugging
      console.log('Sending PATCH request with data:', { id: visitId, ...patchData });

      const result = await updateSiteVisit({ id: visitId, ...patchData }).unwrap();
      console.log('PATCH success result:', result);

      // After successful PATCH, refetch the site visits list
      await refetchSiteVisits();

      return true;
    } catch (err: any) {
      // Enhanced error logging
      console.error("Status update error details:", {
        error: err,
        response: err?.data,
        status: err?.status,
        message: err?.message,
        requestData: { id: visitId, newStatus, driverId, remarks }
      });

      // Show more specific error message to user
      let errorMessage = "Unknown error occurred while updating status";

      if (err?.data?.driver) {
        const driverError = Array.isArray(err.data.driver) ? err.data.driver[0] : err.data.driver;
        errorMessage = `Driver assignment failed: ${driverError}`;
      } else if (err?.data?.marketer) {
        const marketerError = Array.isArray(err.data.marketer) ? err.data.marketer[0] : err.data.marketer;
        errorMessage = `Marketer validation failed: ${marketerError}`;
      } else if (err?.data?.project) {
        const projectError = Array.isArray(err.data.project) ? err.data.project[0] : err.data.project;
        errorMessage = `Project validation failed: ${projectError}`;
      } else if (err?.data?.non_field_errors) {
        const nonFieldError = Array.isArray(err.data.non_field_errors) ? err.data.non_field_errors[0] : err.data.non_field_errors;
        errorMessage = `Validation failed: ${nonFieldError}`;
      } else {
        errorMessage = err?.data?.detail ||
                      err?.data?.error ||
                      err?.message ||
                      errorMessage;
      }

      toast.error(`Failed to update status: ${errorMessage}`);
      return false;
    }
  };

  // Handle site visit deletion - open confirmation dialog
  const handleDeleteSiteVisit = (visitId: number, visitTitle: string) => {
    setVisitToDelete({ id: visitId, title: visitTitle });
    setDeleteDialogOpen(true);
  };

  // Confirm and execute deletion
  const confirmDeleteSiteVisit = async () => {
    if (!visitToDelete) return;

    setIsDeleting(true);
    try {
      await deleteSiteVisit(visitToDelete.id).unwrap();
      toast.success('Site visit deleted successfully!');
      
      // Refetch the site visits list to update the UI
      await refetchSiteVisits();
      
      // Close dialog and reset state
      setDeleteDialogOpen(false);
      setVisitToDelete(null);
    } catch (error: any) {
      console.error('Failed to delete site visit:', error);
      const errorMessage = error?.data?.detail || error?.message || 'Failed to delete site visit';
      toast.error(`Failed to delete site visit: ${errorMessage}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Cancel deletion
  const cancelDeleteSiteVisit = () => {
    setDeleteDialogOpen(false);
    setVisitToDelete(null);
  };


  // Fetch lists
  const {
    data: visitsResp,
    isLoading: visitsLoading,
    error: visitsError,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    // Remove marketer filter to fetch all site visits globally for stats page
    // Order by creation date descending (most recent first)
    // Fallback to pickup_date if created_at is not supported
    ordering: '-created_at,-pickup_date,-id',
  });

  const {
    data: vehicleRequestsResp,
    isLoading: vehicleRequestsLoading,
  } = useGetVehicleRequestsQuery({ page: 1, page_size: 100 });

  const {
    data: vehiclesResp,
    isLoading: vehiclesLoading,
  } = useGetVehiclesQuery({ page: 1, page_size: 100 });

  const {
    data: specialBookingsResp,
    isLoading: specialBookingsLoading,
  } = useGetSpecialBookingsQuery({
    page: 1,
    page_size: 100,
    assigned_to: currentUserEmployeeNo || undefined // Filter by current user
  });

  // —————————————————— Metrics calculation ——————————————————
  const metrics = useMemo(() => {
    const visits = visitsResp?.data?.results || [];
    const vehicleRequests = vehicleRequestsResp?.data?.results || [];
    const vehicles = vehiclesResp?.data?.results || [];
    const specialBookings = specialBookingsResp?.data?.results || [];

    const totalVisits = visits.length;
    const completedVisits = visits.filter((v) => v.status === 'Completed').length;
    const pendingVisits = visits.filter((v) => v.status === 'Pending').length;
    const rejectedVisits = visits.filter((v) =>
      ['Rejected', 'Cancelled'].includes(v.status)
    ).length;

    const totalVehicleRequests = vehicleRequests.length;
    const pendingVehicleRequests = vehicleRequests.filter(
      (r) => r.status === 'Pending'
    ).length;

    const availableVehicles = vehicles.filter((v) => v.status === 'Available').length;
    const inUseVehicles = vehicles.filter((v) => v.status === 'In Use').length;

    const totalSpecialBookings = specialBookings.length;
    const completedSpecialBookings = specialBookings.filter(
      (b) => b.status === 'Completed'
    ).length;

    return [
      {
        title: 'Total Site Visits',
        value: totalVisits,
        icon: MapPin,
        iconBg:
          'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-700',
        iconColor: 'text-blue-600 dark:text-blue-300',
        change: pendingVisits,
        changeLabel: 'pending',
        positive: true,
        cardBg:
          'bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 border-2 border-blue-200 dark:border-blue-700',
      },
      {
        title: 'Completed Visits',
        value: completedVisits,
        icon: CheckCircle,
        iconBg:
          'bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800 dark:to-green-700',
        iconColor: 'text-green-600 dark:text-green-300',
        change: `${Math.round((completedVisits / totalVisits) * 100) || 0}%`,
        changeLabel: 'completion rate',
        positive: true,
        cardBg:
          'bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 border-2 border-green-200 dark:border-green-700',
      },
      {
        title: 'Vehicle Requests',
        value: totalVehicleRequests,
        icon: Car,
        iconBg:
          'bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-800 dark:to-purple-700',
        iconColor: 'text-purple-600 dark:text-purple-300',
        change: pendingVehicleRequests,
        changeLabel: 'pending',
        positive: true,
        cardBg:
          'bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 border-2 border-purple-200 dark:border-purple-700',
      },
      {
        title: 'Available Vehicles',
        value: availableVehicles,
        icon: Truck,
        iconBg:
          'bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-800 dark:to-emerald-700',
        iconColor: 'text-emerald-600 dark:text-emerald-300',
        change: inUseVehicles,
        changeLabel: 'in use',
        positive: true,
        cardBg:
          'bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 border-2 border-emerald-200 dark:border-emerald-700',
      },
      {
        title: 'Special Assignments',
        value: totalSpecialBookings,
        icon: Zap,
        iconBg:
          'bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800 dark:to-orange-700',
        iconColor: 'text-orange-600 dark:text-orange-300',
        change: completedSpecialBookings,
        changeLabel: 'completed',
        positive: true,
        cardBg:
          'bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30 border-2 border-orange-200 dark:border-orange-700',
      },
      {
        title: 'Rejected/Cancelled',
        value: rejectedVisits,
        icon: AlertCircle,
        iconBg:
          'bg-gradient-to-br from-red-100 to-red-200 dark:from-red-800 dark:to-red-700',
        iconColor: 'text-red-600 dark:text-red-300',
        change: `${Math.round((rejectedVisits / totalVisits) * 100) || 0}%`,
        changeLabel: 'rejection rate',
        positive: false,
        cardBg:
          'bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30 border-2 border-red-200 dark:border-red-700',
      },
    ];
  }, [
    visitsResp,
    vehicleRequestsResp,
    vehiclesResp,
    specialBookingsResp,
  ]);

  const isLoading =
    visitsLoading ||
    vehicleRequestsLoading ||
    vehiclesLoading ||
    specialBookingsLoading;

  // Add error handling and debugging for site visits
  useEffect(() => {
    if (visitsError) {
      console.error('Site visits fetch error:', {
        error: visitsError,
        status: (visitsError as any)?.status,
        data: (visitsError as any)?.data,
        message: (visitsError as any)?.message
      });
      toast.error('Failed to load recent site visits. Please refresh the page.');
    }
  }, [visitsError]);

  // Debug logging for site visits data
  useEffect(() => {
    if (visitsResp?.data?.results) {
      console.log('Site visits data loaded:', {
        totalCount: visitsResp.data.results.length,
        firstVisit: visitsResp.data.results[0],
        lastVisit: visitsResp.data.results[visitsResp.data.results.length - 1],
        hasCreatedAt: visitsResp.data.results.some(v => v.created_at),
        sampleDates: visitsResp.data.results.slice(0, 3).map(v => ({
          id: v.id,
          created_at: v.created_at,
          pickup_date: v.pickup_date,
          status: v.status
        }))
      });
    }
  }, [visitsResp]);

  // Filter and paginate visits
  const filteredVisits = useMemo(() => {
    const visits = visitsResp?.data?.results || [];

    // Define valid statuses for recent site visits (excluding 'All' from the filter array)
    // Only show these specific statuses as requested by user
    const validStatuses = ['Pending', 'Approved', 'In Progress', 'Completed', 'Rejected', 'Cancelled', 'Reviewed'];

    // First filter: Only show visits with valid statuses
    const visitsWithValidStatus = visits.filter((visit) => {
      // If status is not defined or not in valid statuses, exclude it
      return visit.status && validStatuses.includes(visit.status);
    });

    // Sort by created_at in descending order (most recent first)
    // Fallback to pickup_date if created_at is not available
    // Then fallback to id as a last resort for consistent ordering
    const sortedVisits = [...visitsWithValidStatus].sort((a, b) => {
      // Primary sort: created_at (most recent first)
      const createdA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const createdB = b.created_at ? new Date(b.created_at).getTime() : 0;

      if (createdA !== createdB) {
        return createdB - createdA; // Descending order
      }

      // Secondary sort: pickup_date (most recent first)
      const pickupA = a.pickup_date ? new Date(a.pickup_date).getTime() : 0;
      const pickupB = b.pickup_date ? new Date(b.pickup_date).getTime() : 0;

      if (pickupA !== pickupB) {
        return pickupB - pickupA; // Descending order
      }

      // Tertiary sort: id (highest first, assuming higher id = more recent)
      return (b.id || 0) - (a.id || 0);
    });

    // Second filter: Apply selected status filter
    if (selectedStatus === 'All') {
      // For 'All', show all valid statuses sorted by status priority in descending order
      const statusPriority = {
        'Pending': 7,
        'Approved': 6,
        'In Progress': 5,
        'Completed': 4,
        'Rejected': 3,
        'Cancelled': 2,
        'Reviewed': 1
      };

      return sortedVisits.sort((a, b) => {
        // First sort by status priority (descending)
        const priorityA = statusPriority[a.status as keyof typeof statusPriority] || 0;
        const priorityB = statusPriority[b.status as keyof typeof statusPriority] || 0;

        if (priorityA !== priorityB) {
          return priorityB - priorityA; // Higher priority first
        }

        // If same status, maintain the original time-based sorting
        const createdA = a.created_at ? new Date(a.created_at).getTime() : 0;
        const createdB = b.created_at ? new Date(b.created_at).getTime() : 0;

        if (createdA !== createdB) {
          return createdB - createdA;
        }

        const pickupA = a.pickup_date ? new Date(a.pickup_date).getTime() : 0;
        const pickupB = b.pickup_date ? new Date(b.pickup_date).getTime() : 0;

        if (pickupA !== pickupB) {
          return pickupB - pickupA;
        }

        return (b.id || 0) - (a.id || 0);
      });
    }

    return sortedVisits.filter((visit) => visit.status === selectedStatus);
  }, [visitsResp?.data?.results, selectedStatus]);

  const paginatedVisits = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredVisits.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredVisits, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredVisits.length / itemsPerPage);

  // Reset current page when items per page changes
  useEffect(() => {
    setCurrentPage(1);
  }, [itemsPerPage]);

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="p-3 sm:p-6 space-y-6 sm:space-y-8">
          {/* Header */}
          <Card className="border-0 shadow-xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 dark:from-blue-700 dark:via-purple-700 dark:to-indigo-700 text-white">
            <CardHeader className="pb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white/20 dark:bg-white/30 backdrop-blur-sm rounded-xl sm:rounded-2xl flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl sm:text-2xl lg:text-3xl font-bold text-white">
                      Logistics Analytics
                    </CardTitle>
                    <CardDescription className="text-blue-100 dark:text-blue-200 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
                      Comprehensive insights into your logistics operations
                    </CardDescription>
                  </div>
                </div>
                <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                  <Badge
                    variant="secondary"
                    className="bg-white/20 dark:bg-white/30 text-white border-white/30 dark:border-white/40 text-xs sm:text-sm"
                  >
                    {isLoading ? 'Syncing...' : 'Live Data'}
                  </Badge>
                  <Badge
                    variant="secondary"
                    className="bg-white/20 dark:bg-white/30 text-white border-white/30 dark:border-white/40 text-xs sm:text-sm"
                  >
                    {format(new Date(), 'MMM dd, yyyy')}
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3 sm:gap-4 lg:gap-6">
            {metrics.map((m, idx) => (
              <div key={idx} className="transform hover:scale-105 transition-all duration-300">
                <Card6
                  title={m.title}
                  value={isLoading ? '...' : m.value}
                  icon={m.icon}
                  iconBg={m.iconBg}
                  iconColor={m.iconColor}
                  change={m.change}
                  changeLabel={m.changeLabel}
                  positive={m.positive}
                  cardBg={m.cardBg}
                />
              </div>
            ))}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
            <Card className="xl:col-span-2 border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="flex items-center text-lg sm:text-xl">
                  <LineChart className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" />
                  Site Visits Booking Trends
                </CardTitle>
                <CardDescription className="text-indigo-100 dark:text-indigo-200 text-sm sm:text-base">
                  Track booking patterns over time
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 bg-white dark:bg-gray-800">
                <SimpleLineChart />
              </CardContent>
            </Card>

            <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-600 dark:from-green-600 dark:to-emerald-700 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="flex items-center text-base sm:text-lg">
                  <PieChart className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Most Booked Sites
                </CardTitle>
                <CardDescription className="text-green-100 dark:text-green-200">
                  Popular destinations
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6 bg-white dark:bg-gray-800">
                <CustomActiveShapePieChart />
              </CardContent>
            </Card>
          </div>

          {/* Special Assignments & Daily Visits */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <Card className="lg:col-span-2 border-0 shadow-xl overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black dark:from-gray-800 dark:via-gray-700 dark:to-gray-900 relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 dark:from-orange-600/30 dark:to-red-600/30"></div>
              <CardHeader className="relative z-10 text-white">
                <CardTitle className="text-xl font-bold flex items-center">
                  <Zap className="w-6 h-6 mr-3 text-orange-400 dark:text-orange-300" />
                  Special Assignments
                </CardTitle>
                <CardDescription className="text-gray-300 dark:text-gray-400">
                  Create and manage special transportation assignments
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10 space-y-4 text-white">
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white/10 dark:bg-white/20 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-2xl font-bold text-orange-400 dark:text-orange-300">
                      {specialBookingsLoading ? (
                        <div className="w-6 h-6 bg-gray-300 animate-pulse rounded"></div>
                      ) : (
                        specialBookingsResp?.data?.results?.length || 0
                      )}
                    </div>
                    <div className="text-xs text-gray-300 dark:text-gray-400">
                      Total Bookings
                    </div>
                  </div>
                  <div className="bg-white/10 dark:bg-white/20 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-2xl font-bold text-yellow-400 dark:text-yellow-300">
                      {specialBookingsLoading ? (
                        <div className="w-6 h-6 bg-gray-300 animate-pulse rounded"></div>
                      ) : (
                        specialBookingsResp?.data?.results?.filter(
                          (b) => b.status === 'Pending'
                        ).length || 0
                      )}
                    </div>
                    <div className="text-xs text-gray-300 dark:text-gray-400">
                      Pending
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-white/20 dark:border-white/30">
                  <CreateASpecialAssignment />
                  <div className="text-xs text-gray-400 dark:text-gray-500">
                    {specialBookingsResp?.data?.results?.filter(
                      (b) => b.status === 'Approved'
                    ).length || 0}{' '}
                    approved •{' '}
                    {specialBookingsResp?.data?.results?.filter(
                      (b) => b.status === 'In Progress'
                    ).length || 0}{' '}
                    in progress
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="lg:col-span-3 border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-cyan-600 dark:from-blue-600 dark:to-cyan-700 text-white rounded-t-lg">
                <CardTitle className="flex items-center text-xl">
                  <BarChart3 className="w-6 h-6 mr-3" />
                  Daily Visits Analytics
                </CardTitle>
                <CardDescription className="text-blue-100 dark:text-blue-200">
                  Last 10 days activity and trends
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6 bg-white dark:bg-gray-800">
                <CustomShapeBarChart />
              </CardContent>
            </Card>
          </div>

          {/* Vehicle Requests Approval Section */}
          <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700">
              <CardTitle className="flex items-center text-lg text-gray-900 dark:text-gray-100">
                <Car className="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
                Pending Vehicle Requests
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                Review and approve vehicle requests with vehicle and driver assignment
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              {vehicleRequestsLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 animate-pulse rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                        <div className="w-1/2 h-3 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {(vehicleRequestsResp?.data?.results || [])
                    .filter((request: any) => request.status === 'Pending')
                    .slice(0, 5)
                    .map((request: any) => (
                      <div
                        key={request.id}
                        className="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                          <Car className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                            {String(request.destination_location || 'N/A')}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            From: {String(request.pickup_location || 'N/A')} •
                            Passengers: {request.number_of_passengers || 0} •
                            Requester: {String(request.requester || 'N/A')}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {request.pickup_date ? format(new Date(request.pickup_date), 'MMM dd, yyyy') : 'N/A'} at {String(request.pickup_time || 'N/A')}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant="outline"
                            className="text-xs bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200"
                          >
                            {String(request.status || 'Unknown')}
                          </Badge>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => handleVehicleRequestApproval(request)}
                            className="bg-purple-600 hover:bg-purple-700 text-white"
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Review
                          </Button>
                        </div>
                      </div>
                    ))}

                  {(vehicleRequestsResp?.data?.results || []).filter((request: any) => request.status === 'Pending').length === 0 && (
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                      </div>
                      <h4 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
                        All Caught Up! 🎉
                      </h4>
                      <p className="text-green-700 dark:text-green-300">
                        No pending vehicle requests to review at the moment.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <div className="flex flex-col gap-4 sm:gap-6">
            <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700 p-4 sm:p-6">
                <CardTitle className="flex flex-wrap items-center gap-2 text-base sm:text-lg text-gray-900 dark:text-gray-100">
                  <div className="flex items-center">
                    <Activity className={`w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-600 dark:text-blue-400 ${visitsLoading ? 'animate-spin' : ''}`} />
                    Recent Site Visits
                  </div>
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs">
                    Enhanced View
                  </Badge>
                  {visitsLoading && (
                    <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 text-xs">
                      Loading...
                    </Badge>
                  )}
                  {visitsError && (
                    <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 text-xs">
                      Error
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
                  Color-coded trips with comprehensive details - Date, Driver, Pickup & Destination
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {/* Status Summary */}
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status Overview</h4>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2 text-xs">
                    {['Pending', 'Approved', 'In Progress', 'Completed', 'Rejected', 'Cancelled', 'Reviewed'].map((status) => {
                      const count = (visitsResp?.data?.results || []).filter((v: any) => v.status === status).length;
                      const statusColor = statusColors[status as keyof typeof statusColors] || statusColors['Pending'];
                      return (
                        <div key={status} className={`p-2 rounded text-center ${statusColor}`}>
                          <div className="font-bold text-sm sm:text-base">{count}</div>
                          <div className="text-xs truncate">{status}</div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Status Tabs */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {['All', 'Pending', 'Approved', 'In Progress', 'Completed', 'Rejected', 'Cancelled', 'Reviewed'].map((status) => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => {
                        setSelectedStatus(status);
                        setCurrentPage(1); // Reset to first page when changing status
                      }}
                      className={`text-xs ${
                        selectedStatus === status
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                      }`}
                    >
                      {status}
                    </Button>
                  ))}
                </div>

                {visitsLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 animate-pulse rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                          <div className="w-1/2 h-3 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    <div className="space-y-3">
                      {paginatedVisits.map((visit: any) => {
                        const statusCellColor = statusCellColors[visit.status as keyof typeof statusCellColors] || statusCellColors['Pending'];
                        const statusBadgeColor = statusColors[visit.status as keyof typeof statusColors] || statusColors['Pending'];

                        return (
                          <div
                            key={visit.id}
                            className={`p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md ${statusCellColor}`}
                          >
                            <div className="flex items-start space-x-3 sm:space-x-4">
                              {/* Status Icon */}
                              <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center ${statusBadgeColor} flex-shrink-0`}>
                                <MapPin className="w-5 h-5 sm:w-6 sm:h-6" />
                              </div>

                              {/* Trip Details */}
                              <div className="flex-1 space-y-2 min-w-0">
                                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                                  <div className="min-w-0 flex-1">
                                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm sm:text-base truncate">
                                      {String(
                                        typeof visit.project === 'object'
                                          ? visit.project?.name || 'N/A'
                                          : visit.project || 'N/A'
                                      )}
                                    </h4>
                                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">
                                      Marketer: {String(
                                        typeof visit.marketer === 'object'
                                          ? visit.marketer?.fullnames ||
                                            visit.marketer?.name ||
                                            'N/A'
                                          : visit.marketer || 'N/A'
                                      )}
                                    </p>
                                  </div>
                                  <Badge className={`${statusBadgeColor} border-0 font-medium text-xs flex-shrink-0`}>
                                    {String(visit.status || 'Unknown')}
                                  </Badge>
                                </div>

                                {/* Trip Information Grid */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-2 sm:gap-3 text-xs sm:text-sm">
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400 block">Date & Time</span>
                                    <span className="font-medium text-gray-900 dark:text-gray-100">
                                      {visit.pickup_date ? format(new Date(visit.pickup_date), 'MMM dd, yyyy') : 'N/A'}
                                      <br />
                                      <span className="text-xs">{String(visit.pickup_time || 'N/A')}</span>
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400 block">Driver</span>
                                    <span className="font-medium text-gray-900 dark:text-gray-100">
                                      {visit.driver ? (
                                        typeof visit.driver === 'object'
                                          ? visit.driver?.fullnames || visit.driver?.name || 'Assigned'
                                          : visit.driver
                                      ) : 'Not Assigned'}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400 block">Pickup Location</span>
                                    <span className="font-medium text-gray-900 dark:text-gray-100">
                                      {String(visit.pickup_location || 'N/A')}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400 block">Destination</span>
                                    <span className="font-medium text-gray-900 dark:text-gray-100">
                                      {String(
                                        visit.special_assignment_destination ||
                                        (typeof visit.project === 'object' ? visit.project?.name : visit.project) ||
                                        'N/A'
                                      )}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex-shrink-0 flex flex-col sm:flex-row gap-1 sm:gap-2">
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="h-8 sm:h-9 px-2 sm:px-3 text-xs sm:text-sm hover:bg-green-50 hover:border-green-300 hover:text-green-700 dark:hover:bg-green-900/50 dark:hover:border-green-700 dark:hover:text-green-400"
                                    >
                                      <Eye className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                                      <span className="hidden sm:inline">View</span>
                                      <span className="sm:hidden">👁</span>
                                    </Button>
                                  </DialogTrigger>
                                  <SiteVisitModal visit={visit} onStatusUpdate={handleStatusUpdate} statusColors={statusColors} />
                                </Dialog>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteSiteVisit(
                                    visit.id,
                                    typeof visit.project === 'object'
                                      ? visit.project?.name || 'Site Visit'
                                      : visit.project || 'Site Visit'
                                  )}
                                  className="h-8 sm:h-9 px-2 sm:px-3 text-xs sm:text-sm hover:bg-red-50 hover:border-red-300 hover:text-red-700 dark:hover:bg-red-900/20 dark:hover:border-red-700 dark:hover:text-red-400"
                                >
                                  <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                                  <span className="hidden sm:inline">Delete</span>
                                  <span className="sm:hidden">🗑</span>
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Pagination Controls */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                        <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 text-center sm:text-left">
                          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredVisits.length)} of {filteredVisits.length} visits
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-600 dark:text-gray-400">Show:</span>
                          <Select value={String(itemsPerPage)} onValueChange={(value) => setItemsPerPage(Number(value))}>
                            <SelectTrigger className="w-16 h-8 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="5">5</SelectItem>
                              <SelectItem value="10">10</SelectItem>
                              <SelectItem value="20">20</SelectItem>
                              <SelectItem value="50">50</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      {totalPages > 1 && (
                        <div className="flex items-center justify-center sm:justify-end space-x-1 sm:space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                          >
                            Previous
                          </Button>
                          <div className="flex items-center space-x-1">
                            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                              let page;
                              if (totalPages <= 5) {
                                page = i + 1;
                              } else if (currentPage <= 3) {
                                page = i + 1;
                              } else if (currentPage >= totalPages - 2) {
                                page = totalPages - 4 + i;
                              } else {
                                page = currentPage - 2 + i;
                              }
                              return (
                                <Button
                                  key={page}
                                  variant={currentPage === page ? 'default' : 'outline'}
                                  size="sm"
                                  onClick={() => setCurrentPage(page)}
                                  className="w-8 h-8 p-0"
                                >
                                  {page}
                                </Button>
                              );
                            })}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                          >
                            Next
                          </Button>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Recent Special Bookings */}
            <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-b border-gray-200 dark:border-gray-700">
                <CardTitle className="flex items-center text-lg text-gray-900 dark:text-gray-100">
                  <Zap className="w-5 h-5 mr-2 text-orange-600 dark:text-orange-400" />
                  Recent Special Bookings
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">
                  Latest special assignments and transportation requests
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {specialBookingsLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 animate-pulse rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                          <div className="w-1/2 h-3 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {(specialBookingsResp?.data?.results || [])
                      .slice(0, 5)
                      .map((booking: any) => (
                        <div
                          key={booking.id}
                          className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center">
                            <Car className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                              {String(booking.destination || 'N/A')}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {(() => {
                                // Handle both object and string formats for assigned_to
                                if (typeof booking.assigned_to === 'object' && booking.assigned_to !== null) {
                                  return String(booking.assigned_to.dp_name || booking.assigned_to.name || 'N/A');
                                } else {
                                  const department = departments.find((dept: any) => String(dept?.dp_id) === String(booking.assigned_to));
                                  return String(department?.dp_name || booking.assigned_to || 'N/A');
                                }
                              })()} • {String(booking.pickup_location || 'N/A')}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant={booking.status === 'Completed' ? 'default' : 'outline'}
                              className={`text-xs ${
                                booking.status === 'Completed'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : booking.status === 'Pending'
                                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                  : booking.status === 'In Progress'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                              }`}
                            >
                              {String(booking.status || 'Unknown')}
                            </Badge>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-orange-100 dark:hover:bg-orange-900/50"
                                >
                                  <Eye className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-md">
                                <DialogHeader>
                                  <DialogTitle className="flex items-center">
                                    <Car className="w-5 h-5 mr-2 text-orange-600" />
                                    Special Booking Details
                                  </DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Assigned To</Label>
                                      <p className="text-sm">{(() => {
                                        // Handle both object and string formats for assigned_to
                                        if (typeof booking.assigned_to === 'object' && booking.assigned_to !== null) {
                                          return String(booking.assigned_to.dp_name || booking.assigned_to.name || 'N/A');
                                        } else {
                                          const department = departments.find((dept: any) => String(dept?.dp_id) === String(booking.assigned_to));
                                          return String(department?.dp_name || booking.assigned_to || 'N/A');
                                        }
                                      })()}</p>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Driver</Label>
                                      <p className="text-sm">{(() => {
                                        const driver = booking.driver;
                                        if (typeof driver === 'object' && driver !== null) {
                                          return String(driver.fullnames || driver.name || driver.employee_no || 'Unknown Driver');
                                        }
                                        return String(driver || 'Not Assigned');
                                      })()}</p>
                                    </div>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-gray-600">Pickup Location</Label>
                                    <p className="text-sm">{String(booking.pickup_location || 'N/A')}</p>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-gray-600">Destination</Label>
                                    <p className="text-sm">{String(booking.destination || 'N/A')}</p>
                                  </div>
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Date</Label>
                                      <p className="text-sm">{booking.reservation_date ? format(new Date(booking.reservation_date), 'MMM dd, yyyy') : 'N/A'}</p>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Time</Label>
                                      <p className="text-sm">{String(booking.reservation_time || 'N/A')}</p>
                                    </div>
                                  </div>
                                  {booking.reason && (
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Reason</Label>
                                      <p className="text-sm">{String(booking.reason)}</p>
                                    </div>
                                  )}
                                  {booking.remarks && (
                                    <div>
                                      <Label className="text-sm font-medium text-gray-600">Remarks</Label>
                                      <p className="text-sm">{String(booking.remarks)}</p>
                                    </div>
                                  )}
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Special Bookings Management Table */}
          <SpecialBookingsTable className="mt-6" />
        </div>

        {/* Vehicle Request Approval Modal */}
        <Dialog open={isVehicleRequestModalOpen} onOpenChange={setIsVehicleRequestModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <DialogHeader>
              <DialogTitle className="flex items-center text-xl text-gray-900 dark:text-gray-100">
                <Car className="w-6 h-6 mr-3 text-purple-600 dark:text-purple-400" />
                Vehicle Request Approval
              </DialogTitle>
              <DialogDescription className="text-gray-600 dark:text-gray-400">
                Review request details and assign vehicle and driver for approval
              </DialogDescription>
            </DialogHeader>

            {selectedVehicleRequest && (
              <div className="space-y-6 py-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Request Details */}
                  <div className="space-y-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                      <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <MapPin className="w-4 h-4 mr-2" />
                        Request Details
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-blue-700 dark:text-blue-300">Requester:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100">
                            {String(selectedVehicleRequest.requester || 'N/A')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700 dark:text-blue-300">Purpose:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100">
                            {String(selectedVehicleRequest.purpose || 'N/A')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700 dark:text-blue-300">Passengers:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100">
                            {selectedVehicleRequest.number_of_passengers || 0}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                      <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        Schedule & Route
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-green-700 dark:text-green-300">Date:</span>
                          <span className="font-medium text-green-900 dark:text-green-100">
                            {selectedVehicleRequest.pickup_date ? format(new Date(selectedVehicleRequest.pickup_date), 'MMMM dd, yyyy') : 'N/A'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700 dark:text-green-300">Time:</span>
                          <span className="font-medium text-green-900 dark:text-green-100">
                            {String(selectedVehicleRequest.pickup_time || 'N/A')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700 dark:text-green-300">From:</span>
                          <span className="font-medium text-green-900 dark:text-green-100">
                            {String(selectedVehicleRequest.pickup_location || 'N/A')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700 dark:text-green-300">To:</span>
                          <span className="font-medium text-green-900 dark:text-green-100">
                            {String(selectedVehicleRequest.destination_location || 'N/A')}
                          </span>
                        </div>
                      </div>
                    </div>

                    {selectedVehicleRequest.remarks && (
                      <div className="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                          Request Remarks
                        </h3>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {selectedVehicleRequest.remarks}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Assignment Section */}
                  <div className="space-y-4">
                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                      <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                        <Car className="w-4 h-4 mr-2" />
                        Vehicle & Driver Assignment
                      </h3>

                      <div className="space-y-4">
                        {/* Vehicle Selection */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                            Select Vehicle *
                          </Label>
                          <Select value={selectedVehicleId} onValueChange={setSelectedVehicleId}>
                            <SelectTrigger className="border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400">
                              <SelectValue placeholder="Choose a vehicle..." />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                              {(vehiclesResp?.data?.results || [])
                                .filter((vehicle: any) => vehicle.status === 'Available')
                                .map((vehicle: any) => (
                                  <SelectItem key={vehicle.id} value={String(vehicle.id)}>
                                    <div className="flex items-center justify-between w-full">
                                      <div className="flex items-center space-x-2">
                                        <Car className="w-4 h-4 text-purple-600" />
                                        <span>{vehicle.make} {vehicle.model} - {vehicle.vehicle_registration}</span>
                                      </div>
                                      <div className="flex items-center space-x-1">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span className="text-xs text-green-600">Available</span>
                                      </div>
                                    </div>
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                          <div className="text-xs text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
                            🚗 Vehicle Booking Rule: One vehicle per time slot - prevents double-booking conflicts
                          </div>
                        </div>

                        {/* Driver Selection */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                            Select Driver *
                          </Label>
                          <Select value={selectedDriverId} onValueChange={setSelectedDriverId}>
                            <SelectTrigger className="border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400">
                              <SelectValue placeholder="Choose a driver..." />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                              {drivers.map((driver: any) => {
                                const driverAssignments = selectedVehicleRequest ?
                                  getDriverAssignments(driver.employee_no, selectedVehicleRequest.pickup_date) : [];

                                return (
                                  <SelectItem key={driver.id} value={String(driver.id)}>
                                    <div className="flex items-center justify-between w-full">
                                      <div className="flex items-center space-x-2">
                                        <User className="w-4 h-4 text-orange-600" />
                                        <div>
                                          <span>
                                            {driver.fullnames || `${driver.first_name} ${driver.last_name}` || driver.username}
                                          </span>
                                          {driverAssignments.length > 0 && (
                                            <div className="text-xs text-gray-500">
                                              {driverAssignments.length} trip(s) assigned
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      <div className="flex items-center space-x-1">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span className="text-xs text-green-600">
                                          Can Handle Multiple
                                        </span>
                                      </div>
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                          <div className="text-xs text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
                            ℹ️ Drivers can handle multiple trips per day - vehicles cannot be double-booked for same time slot
                          </div>
                        </div>

                        {/* Approval Remarks */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                            Approval Remarks (Optional)
                          </Label>
                          <Textarea
                            value={approvalRemarks}
                            onChange={(e) => setApprovalRemarks(e.target.value)}
                            placeholder="Add any remarks about the approval..."
                            className="min-h-[80px] resize-none border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                      <h3 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
                        <Edit className="w-4 h-4 mr-2" />
                        Actions
                      </h3>
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <Button
                            onClick={handleApproveVehicleRequest}
                            disabled={isApprovingRequest || !selectedVehicleId || !selectedDriverId}
                            className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white"
                          >
                            {isApprovingRequest ? (
                              <Activity className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                              <CheckCircle className="w-4 h-4 mr-2" />
                            )}
                            Approve
                          </Button>
                          <Button
                            onClick={handleRejectVehicleRequest}
                            disabled={isApprovingRequest}
                            variant="destructive"
                            className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600"
                          >
                            {isApprovingRequest ? (
                              <Activity className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                              <AlertCircle className="w-4 h-4 mr-2" />
                            )}
                            Reject
                          </Button>
                        </div>
                        {(!selectedVehicleId || !selectedDriverId) && (
                          <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 p-2 rounded">
                            ⚠️ Please select both vehicle and driver before approving the request.
                          </div>
                        )}
                        {selectedVehicleId && selectedVehicleRequest && !checkVehicleAvailability(
                          selectedVehicleId,
                          selectedVehicleRequest.pickup_date,
                          selectedVehicleRequest.pickup_time
                        ) && (
                          <div className="text-xs text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 p-2 rounded">
                            🚫 Vehicle Double-Booking Detected! This vehicle is already assigned to another trip at the same date/time. Please select a different vehicle.
                          </div>
                        )}
                        {selectedVehicleId && selectedVehicleRequest && checkVehicleAvailability(
                          selectedVehicleId,
                          selectedVehicleRequest.pickup_date,
                          selectedVehicleRequest.pickup_time
                        ) && (
                          <div className="text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 p-2 rounded">
                            ✅ Vehicle Available! No conflicts detected for this time slot.
                          </div>
                        )}
                        {selectedDriverId && selectedVehicleRequest && (() => {
                          const selectedDriver = drivers.find((d: any) => d.id === parseInt(selectedDriverId, 10));
                          if (!selectedDriver) return null;

                          const driverAssignments = getDriverAssignments(selectedDriver.employee_no, selectedVehicleRequest.pickup_date);
                          if (driverAssignments.length > 0) {
                            return (
                              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 p-2 rounded">
                                👤 Driver Info: {selectedDriver.fullnames || selectedDriver.username} has {driverAssignments.length} other trip(s) on this date. Drivers can handle multiple trips.
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center text-red-600 dark:text-red-400">
                <Trash2 className="w-5 h-5 mr-2" />
                Delete Site Visit
              </AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
                Are you sure you want to delete the site visit "{visitToDelete?.title}"? 
                This action cannot be undone and will permanently remove all associated data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel 
                onClick={cancelDeleteSiteVisit}
                className="hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteSiteVisit}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white"
              >
                {isDeleting ? (
                  <>
                    <Activity className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Screen>
  );
}