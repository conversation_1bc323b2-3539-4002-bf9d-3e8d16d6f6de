import { SIDEBAR_PERMISSIONS, SidebarPermissionKey, LOGISTICS_PERMISSIONS, LogisticsPermissionKey } from "@/hooks/useSidebarPermissions";

/**
 * Permission checker utility functions for the CRM system
 */

/**
 * Check if a user has a specific permission code
 * @param userPermissions - Array of user's permission codes
 * @param permissionCode - The permission code to check
 * @returns boolean indicating if user has the permission
 */
export const hasPermissionCode = (
  userPermissions: number[],
  permissionCode: number
): boolean => {
  return userPermissions.includes(permissionCode);
};

/**
 * Check if a user has access to a specific sidebar section
 * @param userPermissions - Array of user's permission codes
 * @param section - The sidebar section key
 * @returns boolean indicating if user has access to the section
 */
export const hasSidebarSectionAccess = (
  userPermissions: number[],
  section: SidebarPermissionKey
): boolean => {
  const permissionCode = SIDEBAR_PERMISSIONS[section];
  return hasPermissionCode(userPermissions, permissionCode);
};

/**
 * Check if a user has access to a specific logistics permission
 * @param userPermissions - Array of user's permission codes
 * @param permission - The logistics permission key
 * @returns boolean indicating if user has the logistics permission
 */
export const hasLogisticsPermission = (
  userPermissions: number[],
  permission: LogisticsPermissionKey
): boolean => {
  const permissionCode = LOGISTICS_PERMISSIONS[permission];
  return hasPermissionCode(userPermissions, permissionCode);
};

/**
 * Get all accessible sidebar sections for a user
 * @param userPermissions - Array of user's permission codes
 * @returns Array of accessible sidebar section keys
 */
export const getAccessibleSidebarSections = (
  userPermissions: number[]
): SidebarPermissionKey[] => {
  const accessibleSections: SidebarPermissionKey[] = [];
  
  Object.entries(SIDEBAR_PERMISSIONS).forEach(([key, code]) => {
    if (hasPermissionCode(userPermissions, code)) {
      accessibleSections.push(key as SidebarPermissionKey);
    }
  });
  
  return accessibleSections;
};

/**
 * Check multiple permissions at once
 * @param userPermissions - Array of user's permission codes
 * @param permissionCodes - Array of permission codes to check
 * @returns boolean indicating if user has ALL the specified permissions
 */
export const hasAllPermissions = (
  userPermissions: number[],
  permissionCodes: number[]
): boolean => {
  return permissionCodes.every(code => hasPermissionCode(userPermissions, code));
};

/**
 * Check if user has any of the specified permissions
 * @param userPermissions - Array of user's permission codes
 * @param permissionCodes - Array of permission codes to check
 * @returns boolean indicating if user has ANY of the specified permissions
 */
export const hasAnyPermission = (
  userPermissions: number[],
  permissionCodes: number[]
): boolean => {
  return permissionCodes.some(code => hasPermissionCode(userPermissions, code));
};

/**
 * Filter sidebar sections based on user permissions
 * @param sections - Array of sidebar section configurations
 * @param userPermissions - Array of user's permission codes
 * @returns Filtered array of sections the user has access to
 */
export const filterSidebarSectionsByPermissions = <T extends { permissionKey?: SidebarPermissionKey }>(
  sections: T[],
  userPermissions: number[]
): T[] => {
  return sections.filter(section => {
    if (!section.permissionKey) return true; // Show sections without permission requirements
    return hasSidebarSectionAccess(userPermissions, section.permissionKey);
  });
};

/**
 * Permission descriptions for better UX
 */
export const PERMISSION_DESCRIPTIONS = {
  // Sidebar permissions
  111: "Main Section Access",
  112: "Performance Dashboard Access",
  113: "Teams Management Access",
  114: "Reports Access",
  115: "Analytics Access",
  116: "Services Access",
  117: "Administration Access",

  // Logistics permissions
  201: "Book Site Visit - Allows user to book a site visit or appointment",
  202: "Complete Trips - Allows user to mark trips as completed",
  203: "Create Vehicle Request - Allows user to request a vehicle",
  204: "Create Special Assignment - Allows user to assign or create special assignments",
  205: "Access Logistics Dashboard - Allows user to view the logistics dashboard",
  206: "Access Logistics Statistics - Allows user to view logistics-related statistics",
  207: "Access Clients - Allows user to view clients within logistics module",
  208: "Access Drivers - Allows user to view and manage drivers",
  209: "Access Vehicles - Allows user to view and manage vehicles",
  210: "Access Logistics Reports - Allows user to view logistics reports and analytics",
  211: "Access Site Visit Reports - Allows user to view site visit reports and summaries",
} as const;

/**
 * Get permission description by code
 * @param permissionCode - The permission code
 * @returns Description of the permission
 */
export const getPermissionDescription = (permissionCode: number): string => {
  return PERMISSION_DESCRIPTIONS[permissionCode as keyof typeof PERMISSION_DESCRIPTIONS] || `Permission ${permissionCode}`;
};

/**
 * Permission constants for easy reference
 */
export const PERMISSION_CODES = {
  // Sidebar permissions
  MAIN: 111,
  PERFORMANCE: 112,
  TEAMS: 113,
  REPORTS: 114,
  ANALYTICS: 115,
  SERVICES: 116,
  ADMIN: 117,
} as const;

/**
 * Permission descriptions for display purposes
 */
export const PERMISSION_DESCRIPTIONS = {
  [PERMISSION_CODES.MAIN]: "Access Main on Sidebar",
  [PERMISSION_CODES.PERFORMANCE]: "Access Performance on the sidebar",
  [PERMISSION_CODES.TEAMS]: "Can Access teams on the sidebar",
  [PERMISSION_CODES.REPORTS]: "Can Access Reports on the sidebar",
  [PERMISSION_CODES.ANALYTICS]: "Can access Analytics on the sidebar",
  [PERMISSION_CODES.SERVICES]: "Can access Services on the sidebar",
  [PERMISSION_CODES.ADMIN]: "Can access Admin on the sidebar",
} as const;
