import React, { useEffect, useState } from "react";

import { Screen } from "@/app-components/layout/screen";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import {
  DestructiveButton,
  OutlinedButton,
  PrimaryButton,
  TextLinkButton,
} from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import {
  ArrowRightLeftIcon,
  Edit,
  Pencil,
  Plus,
  Settings,
  User,
  X,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from "@radix-ui/react-dropdown-menu";
import {
  useAddDiaporaTripMemberMutation,
  useAddDiaporaTripMutation,
  useGetDiaporaTripsQuery,
  useGetDiasporaRegionsQuery,
  useLazyGetDiasporaRegionsQuery,
  useLazyGetLeadSourceQuery,
  useUpdateDiaporaTripMutation,
} from "@/redux/slices/propects";
import { formatDate } from "@/utils/formatDate";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import { toast } from "sonner";
import Multiselect from "@/components/custom/forms/Multiselect";
import { countryList } from "@/utils/countryList";
import { searchDebouncer } from "@/utils/debouncers";
interface DiasporaTrips {
  id: number;
  trip_name: string;
  trip_date: string;
  trip_notes: string;
  target_MIB: string;
  visiting_country: string;
  diaspora_region: string;
  lead_source: any;
  manager: any;
  lead_source_name: string;
  manager_name: string;
  marketers: any;
  is_active?: boolean;
}

const DiasporaTrips: React.FC = () => {
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [addMemberModal, setAddMemberModal] = useState<any | null>(null);
  const [addModalOpen, setAddModalOpen] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const {
    data: trips,
    isLoading: trips_loading,
    refetch: refetchTrips,
  } = useGetDiaporaTripsQuery({});

  const { data: rg, isLoading: rg_loading } = useGetDiasporaRegionsQuery({});

  console.log("object", rg);

  const [marketers, setMarketers] = useState<DiasporaTrips["marketers"]>([""]);
  const [tripObj, setTripObj] = useState<DiasporaTrips | null>(null);

  const [updateData, setUpdateData] = useState<DiasporaTrips | null>(null);

  const [lead_source, setlead_source] = useState("");
  const [trip_name, settrip_name] = useState("");
  const [trip_date, settrip_date] = useState("");
  const [trip_notes, settrip_notes] = useState("");
  const [target_MIB, settarget_MIB] = useState("");
  const [manager, setmanager] = useState("");
  const [members, setMembers] = useState([]);
  const [diaspora_region, setdiaspora_region] = useState("");

  const [countryOptions, setCountryOptions] = useState<
    { value: any; label: string }[]
  >([]);
  const [selectedCountry, setSelectedCountry] = useState<{
    label: string;
    value: string;
  } | null>(null);

  useEffect(() => {
    const countrySelectOptionsList = countryList?.map((country: any) => ({
      value: country?.label,
      label: `${country?.icon} ${country?.label}`,
    }));
    setCountryOptions(countrySelectOptionsList);
  }, [countryList]);

  // update field when edit is clicked
  useEffect(() => {
    if (updateData) {
      setlead_source(updateData?.lead_source);
      settrip_name(updateData?.trip_name);
      settrip_date(updateData?.trip_date);
      settrip_notes(updateData?.trip_notes);
      settarget_MIB(updateData?.target_MIB);
      setmanager(updateData?.manager);
      setdiaspora_region(updateData?.diaspora_region);
      const country = countryOptions.find(
        (c) => c.value === updateData?.visiting_country
      );
      setSelectedCountry(country || null);
    }
  }, [updateData, countryOptions]);

  const [createTrip, { data: newTrip, isLoading: adding }] =
    useAddDiaporaTripMutation();
  const [createTripMember, { data: newMembers, isLoading: addingMembers }] =
    useAddDiaporaTripMemberMutation();
  const [updateTrip, { data: updatedTrip, isLoading: updating }] =
    useUpdateDiaporaTripMutation();
  const [fetchLeadSources, { data: lsData, isLoading: lsLoading }] =
    useLazyGetLeadSourceQuery();
  const [fetchUsers, { data: userData, isLoading: usersLoading }] =
    useLazyGetUsersQuery();
  const [fetchRegions, { data: regions, isLoading: loadingRegions }] =
    useLazyGetDiasporaRegionsQuery();

  // update trip status
  const handleUpdateStatus = async (trip: any) => {
    try {
      const res = await updateTrip({
        ...trip,
        is_active: !trip?.is_active,
      }).unwrap();
      if (res?.id) {
        toast.success(
          `Trip ${trip?.is_active ? "deactivated" : "activated"} successfully`
        );
        refetchTrips();
      } else {
        toast.error("Failed to update");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  const columns: ColumnDef<DiasporaTrips>[] = [
    {
      id: "trip_name",
      accessorKey: "trip_name",
      header: "Name",
      enableHiding: true,
      cell: (info) => {
        return (
          <span className="font-sm pl-2">
            {(info.getValue() as string).toUpperCase()}
          </span>
        );
      },
      enableColumnFilter: false,
    },

    {
      id: "visiting_country",
      accessorKey: "visiting_country",
      header: "Visiting Country",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },

    {
      id: "trip_date",
      accessorKey: "trip_date",
      header: "Trip Date",
      enableHiding: true,
      cell: (info) => (
        <span className="flex gap-1">
          <span>{formatDate(info.getValue())}</span>
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "target_MIB",
      accessorKey: "target_MIB",
      header: "MIB Target",
      enableHiding: true,
      cell: (info) => (
        <span className="font-bold">
          {formatMoney(info.getValue() as string, "Ksh")}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "manager_name",
      header: "Manager",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <span className="flex gap-1">
            {row?.original?.manager_name == ""
              ? "--"
              : row?.original?.manager_name}
          </span>
        </div>
      ),

      enableColumnFilter: false,
    },
    {
      id: "marketers",
      header: "Marketers",
      cell: ({ row }) => {
        return (
          <TextLinkButton
            className="border-primary "
            variant="primary"
            key={2}
            onClick={() => {
              marketerHandler(row?.original?.marketers, row?.original);
            }}
          >
            ({row?.original?.marketers?.length}) Marketers
          </TextLinkButton>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "is_active",
      header: "Status",
      cell: ({ row }) => (
        <span
          className={`font-medium ${
            row.original.is_active ? "text-green-500" : "text-red-500"
          }`}
        >
          {row.original.is_active ? "Active" : "Inactive"}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-1 max-w-10 justify-center items-center">
          <PrimaryButton
            variant="outline"
            size="sm"
            onClick={() => {
              setUpdateData(row?.original), setAddModalOpen(true);
            }}
            className="bg-white !text-blue-500 !border-blue-300 hover:!bg-blue-50 flex items-center space-x-1"
          >
            <span title="Edit">
              <Edit />
            </span>
          </PrimaryButton>
          <PrimaryButton
            variant="outline"
            size="sm"
            onClick={() => {
              handleUpdateStatus(row?.original);
            }}
            className={`bg-white flex items-center space-x-1 ${
              row?.original?.is_active
                ? "!text-green-500 !border-green-300 hover:!bg-green-50"
                : "!text-red-500 !border-red-300 hover:!bg-red-50"
            }`}
          >
            <span title="Update Status">
              <ArrowRightLeftIcon />
            </span>
          </PrimaryButton>
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  const marketerHandler = (
    marketers: DiasporaTrips["marketers"],
    tripObject: DiasporaTrips
  ) => {
    setMarketers(marketers);
    setTripObj(tripObject);
    setIsOpen(true);
  };

  const formatMoney = (value: string, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(parseInt(value));
  };

  const handleAddTrip = async (trip?: any) => {
    let formData = {
      lead_source,
      trip_name,
      trip_date,
      trip_notes,
      target_MIB,
      manager,
      diaspora_region,
      visiting_country: selectedCountry ? selectedCountry.value : "",
    };

    try {
      let res;
      if (trip) {
        res = await updateTrip(formData).unwrap();
      } else {
        res = await createTrip(formData).unwrap();
      }
      if (res?.id) {
        toast.success(`Trip ${trip ? "updated" : "added"} successfully`);
        setAddModalOpen(false);
      } else {
        toast.error("Failed to add");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  const handleAddMember = async () => {
    if (!addMemberModal) return;
    const memberData = {
      trip: addMemberModal?.id,
      members: members,
    };
    try {
      const res = await createTripMember(memberData).unwrap();
      if (res?.success) {
        toast.success("Members added successfully");
        refetchTrips();
        setAddMemberModal(null);
      } else {
        toast.error("Failed to add members");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <Screen>
      {trips_loading ? (
        <SpinnerTemp type="spinner-double" />
      ) : (
        <div className="w-full h-full px-2 py-4">
          <div className="flex flex-col gap-4  w-full h-full ">
            <div className="flex flex-wrAap justify-between items-center gap-2">
              <h1 className="font-medium text-xl">Diaspora Trips</h1>
              <Button
                variant="default"
                className="flex items-center gap-2"
                onClick={() => setAddModalOpen(true)}
              >
                <Plus />
                <span>Add New Trip</span>
              </Button>
            </div>
            <DataTable
              containerClassName="bg-slate-50 dark:bg-inherit !overflow-x-auto"
              columns={columns}
              data={trips?.data?.results || []}
              enableSelectColumn={true}
              enableToolbar={true}
              enableExportToExcel={true}
              enablePrintPdf={true}
              enablePagination={true}
              tBodyCellsClassName=""
              tBodyTrClassName="!items-start "
              searchInput={
                <input
                  value={searchInput}
                  name="searchInput"
                  type="search"
                  onChange={(e) =>
                    searchDebouncer(
                      e.target.value,
                      setSearchInput,
                      setSearchValue
                    )
                  }
                  className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Search prospects..."
                />
              }
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              setItemsPerPage={setItemsPerPage}
              totalItems={trips?.data?.total_data || 0}
            />
          </div>
        </div>
      )}

      {/* add / edit trip modal  */}
      {addModalOpen && (
        <BaseModal
          isOpen={addModalOpen}
          onOpenChange={setAddModalOpen}
          title="Add New Trip"
          size="lg"
          description="Fill in the details of the new trip"
        >
          <div className="py-4 flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">
                lead_source &nbsp;
                {updateData && (
                  <small className="text-primary">
                    (Current:{" "}
                    {lead_source ? updateData?.lead_source_name : "None"})
                  </small>
                )}
              </label>
              <CustomSelectField
                valueField="leadsource_id"
                labelField="name"
                data={lsData?.data?.results}
                queryFunc={fetchLeadSources}
                setValue={setlead_source}
                loader={lsLoading}
                useSearchField={true}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Trip Name</label>
              <input
                type="text"
                className="p-1 bg-inherit border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={trip_name}
                onChange={(e) => settrip_name(e.target.value)}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">
                Trip Date &nbsp;{" "}
                {updateData && (
                  <small className="text-primary">
                    (Current: {formatDate(updateData?.trip_date)})
                  </small>
                )}
              </label>
              <input
                type="date"
                className="p-1 bg-inherit border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={trip_date}
                onChange={(e) => settrip_date(e.target.value)}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">MIB Target</label>
              <input
                type="text"
                className="p-1 border bg-inherit rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={target_MIB}
                onChange={(e) => settarget_MIB(e.target.value)}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">
                Visiting Country &nbsp;
                {updateData && (
                  <small className="text-primary">
                    (Current: {selectedCountry ? selectedCountry.label : "None"}
                    )
                  </small>
                )}
              </label>

              <Multiselect
                value={selectedCountry}
                data={countryOptions}
                setValue={setSelectedCountry}
                loading={false}
                isClearable={false}
                isDisabled={false}
                isMultiple={false}
                isSearchable={true}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">
                Trip Manager &nbsp;
                {updateData && (
                  <small className="text-primary">
                    (Current: {updateData?.manager_name})
                  </small>
                )}
              </label>
              <CustomSelectField
                valueField="employee_no"
                labelField="fullnames"
                data={userData?.data?.results}
                queryFunc={fetchUsers}
                setValue={setmanager}
                loader={usersLoading}
                useSearchField={true}
              />
            </div>{" "}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">
                Diaspora Region &nbsp;
                {updateData && (
                  <small className="text-primary">
                    (Current: {updateData?.diaspora_region})
                  </small>
                )}
              </label>
              <CustomSelectField
                valueField="id"
                labelField="name"
                data={regions?.data?.results}
                queryFunc={fetchRegions}
                setValue={setdiaspora_region}
                loader={loadingRegions}
                useSearchField={true}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Trip Notes</label>
              <textarea
                className="p-1 bg-inherit border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={trip_notes}
                onChange={(e) => settrip_notes(e.target.value)}
                rows={2}
              ></textarea>
            </div>{" "}
            {adding || updating ? (
              <SpinnerTemp type="spinner-double" />
            ) : updateData ? (
              <Button
                variant="default"
                className="mt-4"
                onClick={() => handleAddTrip(updateData)}
              >
                Update Trip
              </Button>
            ) : (
              <Button
                variant="default"
                className="mt-4"
                onClick={() => handleAddTrip()}
              >
                Add Trip
              </Button>
            )}
          </div>
        </BaseModal>
      )}

      {/* add / edit trip modal  */}
      {addMemberModal && (
        <BaseModal
          isOpen={!!addMemberModal}
          onOpenChange={setAddMemberModal}
          title="Add New Member Trip"
          description="Select one or multiple members to add to this trip"
        >
          <div className="py-4 flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Members</label>
              <CustomSelectField
                valueField="employee_no"
                labelField="fullnames"
                data={userData?.data?.results}
                queryFunc={fetchUsers}
                setValue={setMembers}
                loader={usersLoading}
                useSearchField={true}
                isMultiple={true}
              />
            </div>{" "}
            {addingMembers ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button
                variant="default"
                className="mt-4"
                onClick={() => handleAddMember()}
              >
                Submit
              </Button>
            )}
          </div>
        </BaseModal>
      )}

      {/* show marketer modal  */}
      <BaseModal
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        title={`${tripObj?.trip_name.toUpperCase()} Marketers`}
        description=""
        footer={
          <>
            <PrimaryButton onClick={() => setAddMemberModal(tripObj)}>
              Add Member
            </PrimaryButton>
            <DestructiveButton onClick={() => setIsOpen(false)}>
              Close
            </DestructiveButton>
          </>
        }
      >
        <div className="py-4 flex flex-col gap-2">
          {/* For API integration the marketers need a marketer*/}
          {marketers &&
            marketers?.length > 0 &&
            marketers?.map((mk: any) => (
              <div
                key={mk?.id}
                className="flex flex-row items-center justify-between w-full px-4 py-3 
                        bg-white dark:bg-gray-800 
                        rounded-lg border border-gray-100 dark:border-gray-700 
                        shadow-xs hover:shadow-sm dark:shadow-none 
                        transition-all duration-200"
              >
                <span className="text-gray-600 dark:text-gray-300 font-medium flex gap-2">
                  <span>
                    <User />
                  </span>
                  {mk?.name}
                </span>
                {/* <OutlinedButton
                  size="sm"
                  className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                  aria-label="Remove item"
                >
                  <X className="w-4 h-4 text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 transition-colors" />
                </OutlinedButton> */}
              </div>
            ))}
        </div>
      </BaseModal>
    </Screen>
  );
};
export default DiasporaTrips;
